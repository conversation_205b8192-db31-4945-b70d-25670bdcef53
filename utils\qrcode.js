/**
 * 二维码生成工具
 * 解决uniapp中canvas在scroll-view中的层级问题
 * 参考：https://blog.csdn.net/qq_44741577/article/details/149716555
 */

/**
 * 生成二维码并转换为图片
 * @param {Object} options 配置选项
 * @param {string} options.text 二维码内容
 * @param {string} options.canvasId canvas ID
 * @param {number} options.size 二维码大小，默认300
 * @param {Object} options.componentInstance 组件实例
 * @returns {Promise<string>} 返回图片临时路径
 */
export function generateQRCodeImage(options) {
  const {
    text,
    canvasId,
    size = 300,
    componentInstance
  } = options

  return new Promise((resolve, reject) => {
    try {
      // 创建canvas上下文
      const ctx = uni.createCanvasContext(canvasId, componentInstance)
      
      // 设置画布背景
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(0, 0, size, size)
      
      // 绘制二维码
      drawQRCode(ctx, text, size)
      
      // 绘制完成后转换为图片
      ctx.draw(false, () => {
        // 延迟转换，确保绘制完成
        setTimeout(() => {
          uni.canvasToTempFilePath({
            canvasId: canvasId,
            success: (res) => {
              resolve(res.tempFilePath)
            },
            fail: (err) => {
              console.error('二维码转换图片失败:', err)
              reject(err)
            }
          }, componentInstance)
        }, 1000) // 关键：必须等待canvas绘制完成
      })
    } catch (error) {
      console.error('二维码生成失败:', error)
      reject(error)
    }
  })
}

/**
 * 绘制二维码（简化版实现）
 * 实际项目中建议使用专业的二维码库如 qrcode.js
 * @param {Object} ctx canvas上下文
 * @param {string} text 二维码内容
 * @param {number} size 画布大小
 */
function drawQRCode(ctx, text, size) {
  const moduleSize = Math.floor(size / 25) // 模块大小
  const margin = Math.floor(size * 0.1) // 边距
  const qrSize = size - margin * 2 // 二维码实际大小
  
  ctx.setFillStyle('#000000')
  
  // 绘制定位点
  drawPositionMarker(ctx, margin, margin, moduleSize)
  drawPositionMarker(ctx, size - margin - moduleSize * 7, margin, moduleSize)
  drawPositionMarker(ctx, margin, size - margin - moduleSize * 7, moduleSize)
  
  // 绘制数据模块（简化版，实际应根据二维码算法生成）
  drawDataModules(ctx, text, margin, moduleSize, qrSize)
  
  // 绘制文字标识（可选）
  if (text.length < 50) {
    ctx.setFillStyle('#333333')
    ctx.setFontSize(Math.max(12, moduleSize))
    ctx.setTextAlign('center')
    ctx.fillText(text.substring(0, 20) + (text.length > 20 ? '...' : ''), size / 2, size - margin / 2)
  }
}

/**
 * 绘制定位点
 * @param {Object} ctx canvas上下文
 * @param {number} x x坐标
 * @param {number} y y坐标
 * @param {number} moduleSize 模块大小
 */
function drawPositionMarker(ctx, x, y, moduleSize) {
  const markerSize = moduleSize * 7
  
  // 外框
  ctx.fillRect(x, y, markerSize, markerSize)
  
  // 内部白色区域
  ctx.setFillStyle('#ffffff')
  ctx.fillRect(x + moduleSize, y + moduleSize, markerSize - moduleSize * 2, markerSize - moduleSize * 2)
  
  // 中心黑色方块
  ctx.setFillStyle('#000000')
  ctx.fillRect(x + moduleSize * 2, y + moduleSize * 2, markerSize - moduleSize * 4, markerSize - moduleSize * 4)
}

/**
 * 绘制数据模块
 * @param {Object} ctx canvas上下文
 * @param {string} text 文本内容
 * @param {number} margin 边距
 * @param {number} moduleSize 模块大小
 * @param {number} qrSize 二维码大小
 */
function drawDataModules(ctx, text, margin, moduleSize, qrSize) {
  // 简化的数据模块绘制
  // 实际项目中应使用专业的二维码算法
  
  const modules = Math.floor(qrSize / moduleSize)
  const seed = hashCode(text) // 根据文本生成种子
  
  for (let i = 0; i < modules; i++) {
    for (let j = 0; j < modules; j++) {
      // 跳过定位点区域
      if (isPositionMarkerArea(i, j, modules)) {
        continue
      }
      
      // 根据种子和位置决定是否绘制模块
      if (shouldDrawModule(i, j, seed)) {
        ctx.fillRect(
          margin + i * moduleSize,
          margin + j * moduleSize,
          moduleSize - 1, // 留1px间隙
          moduleSize - 1
        )
      }
    }
  }
}

/**
 * 判断是否为定位点区域
 * @param {number} i 行索引
 * @param {number} j 列索引
 * @param {number} modules 模块总数
 * @returns {boolean}
 */
function isPositionMarkerArea(i, j, modules) {
  // 左上角定位点
  if (i < 9 && j < 9) return true
  // 右上角定位点
  if (i >= modules - 9 && j < 9) return true
  // 左下角定位点
  if (i < 9 && j >= modules - 9) return true
  
  return false
}

/**
 * 根据位置和种子决定是否绘制模块
 * @param {number} i 行索引
 * @param {number} j 列索引
 * @param {number} seed 种子值
 * @returns {boolean}
 */
function shouldDrawModule(i, j, seed) {
  // 简单的伪随机算法，实际应使用二维码编码算法
  const hash = (i * 31 + j * 17 + seed) % 100
  return hash > 45 // 约50%的模块被绘制
}

/**
 * 字符串哈希函数
 * @param {string} str 字符串
 * @returns {number} 哈希值
 */
function hashCode(str) {
  let hash = 0
  if (str.length === 0) return hash
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  return Math.abs(hash)
}

/**
 * 使用专业二维码库的示例（需要安装相应的库）
 * 推荐使用 weapp-qrcode 或类似的库
 */
export function generateQRCodeWithLibrary(options) {
  // 这里可以集成专业的二维码库
  // 例如：weapp-qrcode, qrcode.js 等
  console.log('建议集成专业的二维码库，如 weapp-qrcode')
  return generateQRCodeImage(options)
}
