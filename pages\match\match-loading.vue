<template>
	<view class="match-loading">
		<view class="content">
			<!-- <view class="tip-text">
				95%的来访者表示匹配到了理想的咨询师，且体验了更省钱、更高效的咨询服务。
			</view> -->

			<view class="matching-card">

				<view class="avatar-container">
					<!-- <view class="circle-animation"></view> -->
					<view class="avatar">
						<image src="../../static/icon/组 <EMAIL>" mode="aspectFill"></image>
					</view>
				</view>

				<view class="title">请耐心等待咨询师匹配中~</view>
				<view class="steps">
					<view class="step-container" :class="{ 'active': step === 0, 'done': step > 0 }">
						<view class="step-box">
							<view class="step-item">
								<text>问题正在分析中...</text>
								<view class="step-animation">
									<image v-if="step === 0" class="rotating-icon" src="../../static/icon/形状 <EMAIL>"></image>
									<view v-else-if="step > 0" class="step-icon">
										<image src="../../static/icon/形状 <EMAIL>"></image>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="step-container" :class="{ 'active': step === 1, 'done': step > 1 }">
						<view class="step-box">
							<view class="step-item">
								<text>寻找匹配的咨询师...</text>
								<view class="step-animation">
									<image v-if="step === 1" class="rotating-icon" src="../../static/icon/形状 <EMAIL>"></image>
									<view v-else-if="step > 1" class="step-icon">
										<image src="../../static/icon/形状 <EMAIL>"></image>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="step-container" :class="{ 'active': step === 2, 'done': step > 2 }">
						<view class="step-box">
							<view class="step-item">
								<text>分析咨询师匹配适度...</text>
								<view class="step-animation">
									<image v-if="step === 2" class="rotating-icon" src="../../static/icon/形状 <EMAIL>"></image>
									<view v-else-if="step > 2" class="step-icon">
										<image src="../../static/icon/形状 <EMAIL>"></image>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="step-container" :class="{ 'active': step === 3, 'done': step > 3 }">
						<view class="step-box">
							<view class="step-item">
								<text>返回最佳的匹配结果...</text>
								<view class="step-animation">
									<image v-if="step === 3" class="rotating-icon" src="../../static/icon/形状 <EMAIL>"></image>
									<view v-else-if="step > 3" class="step-icon">
										<image src="../../static/icon/形状 <EMAIL>"></image>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getAllConsultantsSimple } from '@/api/match.js';

const step = ref(0);
const consultants = ref([]);
const currentConsultant = ref(null);
let avatarTimer = null;

// 随机切换咨询师头像
const changeAvatar = () => {
	if (consultants.value.length > 0) {
		const randomIndex = Math.floor(Math.random() * consultants.value.length);
		currentConsultant.value = consultants.value[randomIndex];
	}
};

// 获取咨询师列表
const getConsultants = async () => {
	try {
		const res = await getAllConsultantsSimple();
		if (res.code === 200) {
			consultants.value = res.data;
			changeAvatar(); // 初始化显示一个咨询师头像

			// 开始定时切换头像
			avatarTimer = setInterval(() => {
				changeAvatar();
			}, 1000); // 每秒切换一次
		}
	} catch (error) {
		console.error('获取咨询师列表失败：', error);
	}
};

// 模拟匹配进度
const startMatching = () => {
	let timer = setInterval(() => {
		if (step.value < 4) {
			step.value++;
		} else {
			clearInterval(timer);
			if (avatarTimer) {
				clearInterval(avatarTimer); // 清除头像切换定时器
			}
			// 匹配完成后直接跳转
			uni.redirectTo({
				url: '/pages/match/match-result'
			});
		}
	}, 1500);
};

onMounted(() => {
	getConsultants();
	startMatching();
});
</script>

<style lang="scss" scoped>
.match-loading {
	min-height: 100vh;
	background: #f5f5f5;
	padding: 32rpx;

	.tip-text {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 32rpx;
	}

	.matching-card {
		padding: 40rpx;

		.title {
			font-size: 36rpx;
			font-weight: 500;
			color: #333;
			text-align: center;
			margin-bottom: 48rpx;
		}

		.avatar-container {
			position: relative;
			width: 589rpx;
			height: 596rpx;
			margin: 0 auto 40rpx;

			// .circle-animation {
			// 	position: absolute;
			// 	top: -20rpx;
			// 	left: -20rpx;
			// 	right: -20rpx;
			// 	bottom: -20rpx;
			// 	border: 4rpx solid #5e72e4;
			// 	border-radius: 50%;
			// 	animation: rotate 2s linear infinite;

			// 	&::before {
			// 		content: '';
			// 		position: absolute;
			// 		top: -4rpx;
			// 		left: 50%;
			// 		width: 20rpx;
			// 		height: 20rpx;
			// 		background: #5e72e4;
			// 		border-radius: 50%;
			// 		transform: translateX(-50%);
			// 	}
			// }

			.avatar {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: #f0f3ff;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				overflow: hidden;
				animation: rotate 2s linear infinite;

				image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}

		.steps {
			background: #fff;
			border-radius: 12rpx;
			padding: 34rpx;

			.step-container {
				// margin-bottom: 32rpx;

				.step-box {
					position: relative;
					transition: all 0.3s ease;

					&::before {
						content: '';
						position: absolute;
						top: -2rpx;
						left: -2rpx;
						right: -2rpx;
						bottom: -2rpx;
						border: 2rpx solid #5e72e4;
						border-radius: 12rpx;
						opacity: 0;
						transition: all 0.3s ease;
					}
				}

				.step-item {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.step-animation {
						position: relative;
						width: 34rpx;
						height: 34rpx;
						display: flex;
						justify-content: center;
						align-items: center;

						.rotating-icon {
							width: 34rpx;
							height: 34rpx;
							animation: stepRotate 1s linear infinite;
						}

						.step-icon {
							width: 34rpx;
							height: 34rpx;
							background-color: #AEAAAC;
							display: flex;
							justify-content: center;
							align-items: center;
							border-radius: 50%;
							animation: fadeIn 0.5s ease-in-out;

							image {
								width: 20rpx;
								height: 20rpx;
							}
						}
					}

					text {
						font-size: 28rpx;
						color: #000;
						transition: color 0.3s ease;
					}
				}

				&.active {}

				// &.done {
				// 	.step-box {
				// 		background: rgba(94, 114, 228, 0.1);
				// 		border-color: #5e72e4;
				// 	}

				// 	.step-item {
				// 		text {
				// 			color: #5e72e4;
				// 		}

				// 		.step-animation .step-icon {
				// 			background-color: #5e72e4;

				// 			image {
				// 				filter: brightness(0) invert(1);
				// 			}
				// 		}
				// 	}
				// }
			}
		}
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

@keyframes stepRotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: scale(0.8);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}
</style>