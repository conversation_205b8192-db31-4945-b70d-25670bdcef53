<template>
  <view class="my-meditations-page">
    <!-- 顶部是 result-category-tabs 组件 -->
    <view class="result-category-tabs">
      <view v-for="(tab, index) in tabList" :key="tab.key" :class="['tab-item', { active: currentTab === tab.key }]"
        @click="switchTab(tab.key)">
        {{ tab.name }}
        <image v-if="currentTab === tab.key" class="tab-icon" src="../../../static/icon/形状 6-active.png"></image>
      </view>
    </view>

    <!-- 冥想列表 -->
    <scroll-view class="meditation-list" scroll-y>
      <!-- 空状态 -->
      <view class="empty" v-if="currentList.length === 0">
        <text>暂无冥想记录</text>
      </view>

      <!-- 使用 OrderListItem 组件 -->
      <view v-else class="list-container">
        <OrderListItem v-for="item in currentList" :key="item.id" :item="item" :type="'meditation'"
          @click="handleItemClick" @action="handleAction" />
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import OrderListItem from '@/components/OrderListItem/OrderListItem.vue'
import {
  getPurchasedMeditations,
  getUserMeditationRecords,
  getUserMeditationStatistics
} from '@/api/meditation'
import { useUserStore } from '@/stores/user'

// 响应式数据
const currentTab = ref('all')
const meditationList = ref([])

// 分类标签配置
const tabList = [
  { key: 'all', name: '全部' },
  { key: 'purchased', name: '已购买' },
  { key: 'completed', name: '已完成' }
]

// 冥想订单数据
const meditationOrders = ref([
  {
    id: 201,
    meditationName: '正念冥想',
    meditationCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
    status: 'pending',
    duration: 15,
    createTime: '07/08 14:00',
    actualPrice: '9.9',
    type: 'meditation'
  },
  {
    id: 202,
    meditationName: '睡眠冥想',
    meditationCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
    status: 'completed',
    duration: 20,
    createTime: '07/07 10:00',
    actualPrice: '19.9',
    type: 'meditation'
  }
])

const userStore = useUserStore()

// 计算当前分类下的列表
const currentList = computed(() => {
  // 将冥想数据转换为 OrderListItem 组件期望的格式
  const transformedList = meditationOrders.value.map(item => ({
    id: item.id,
    meditationName: item.meditationName,
    meditationCover: item.meditationCover,
    status: item.status,
    duration: item.duration,
    createTime: item.createTime,
    actualPrice: item.actualPrice,
    type: 'meditation',
    // 保留原始数据
    originalData: item
  }))

  // 根据选择的分类筛选
  if (currentTab.value === 'all') {
    return transformedList
  }
  return transformedList.filter(item => {
    if (currentTab.value === 'purchased') {
      return item.status === 'pending' || item.status === 'in_progress'
    }
    if (currentTab.value === 'completed') {
      return item.status === 'completed'
    }
    return true
  })
})

// 方法
const switchTab = (key) => {
  currentTab.value = key
}

// 处理 OrderListItem 组件的点击事件
const handleItemClick = (item) => {
  console.log('点击冥想项:', item)
  // 跳转到冥想详情页
  uni.navigateTo({
    url: `/pages/meditation/detail/index?id=${item.id}`
  })
}

// 处理 OrderListItem 组件的操作事件
const handleAction = ({ action, item }) => {
  console.log('处理操作:', action, item)
  const originalData = item.originalData

  switch (action) {
    case 'detail':
      handleItemClick(item)
      break
    case 'use':
      // 开始冥想
      uni.navigateTo({
        url: `/pages/meditation/player/index?id=${item.id}`
      })
      break
    case 'continue':
      // 继续冥想
      uni.navigateTo({
        url: `/pages/meditation/player/index?id=${item.id}`
      })
      break
    default:
      handleItemClick(item)
      break
  }
}

// 加载冥想数据
const loadMeditationData = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    // 这里可以调用实际的API获取冥想数据
    // const res = await getPurchasedMeditations()
    // meditationOrders.value = res.data || []
    console.log('加载冥想数据')
  } catch (error) {
    console.error('获取冥想数据失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 页面加载时初始化数据
onLoad(() => {
  loadMeditationData()
})


</script>

<style lang="scss" scoped>
.my-meditations-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

// 搜索结果分类标签样式
.result-category-tabs {
  display: flex;
  background: #fff;
  padding: 32rpx;
  padding-top: 47rpx;
  padding-bottom: 28rpx;
  justify-content: space-between;

  // border-bottom: 1px solid #eee;
  .tab-item {
    position: relative;
    // padding: 24rpx 32rpx;
    font-size: 28rpx;
    color: #8A8788;
    position: relative;
    margin-right: 148rpx;

    &.active {
      font-size: 32rpx;
      color: #A04571;
      display: flex;
      flex-direction: column;
      align-items: center;

      .tab-icon {
        width: 28rpx;
        height: 12rpx;
        margin-top: 10rpx;
      }
    }
  }

  .tab-item:last-child {
    margin-right: 0;
  }

  .tab-icon {
    width: 26rpx;
    height: 26rpx;
  }
}


.meditation-list {
  width: calc(100vw - 64rpx);
  height: calc(100vh - 186rpx);
  padding: 24rpx 32rpx;

  .list-container {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
}

// .empty {
//   text-align: center;
//   padding: 100rpx 0;
//   color: #999;
//   font-size: 28rpx;
// }

.list-container {
  padding-bottom: 20rpx;
}
</style>
