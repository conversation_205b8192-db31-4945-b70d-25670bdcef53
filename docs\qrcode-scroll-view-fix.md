# 解决uniapp中canvas绘制二维码在scroll-view中的层级问题

## 问题描述

在uniapp中使用canvas绘制二维码时，当页面包含scroll-view组件时，会出现以下问题：

1. **层级问题**：canvas作为原生组件，层级最高，会遮盖其他元素
2. **滚动问题**：canvas不随页面滚动，出现悬浮效果
3. **布局问题**：无法通过CSS控制canvas的层级和位置

## 问题原因

根据uniapp官方文档，原生组件（包括canvas）有以下限制：

- 组件的层级是最高的，所有其他组件无法盖在原生组件上
- 原生组件无法在 scroll-view、swiper、picker-view、movable-view 中正常使用
- 无法对原生组件设置 CSS 动画
- 不能在父级节点使用 overflow:hidden 来裁剪原生组件的显示区域

## 解决方案

### 核心思路

将canvas绘制的二维码转换为图片显示，避免canvas的层级问题：

1. 使用canvas绘制二维码（隐藏canvas）
2. 将canvas内容转换为临时图片
3. 显示图片而不是canvas
4. 确保转换时机正确

### 实现步骤

#### 1. 模板结构

```vue
<template>
  <view class="qr-code">
    <!-- canvas绘制二维码（隐藏） -->
    <canvas
      v-show="!qrCodeImageSrc"
      :id="canvasId"
      :canvas-id="canvasId"
      style="width: 300rpx; height: 300rpx"
    >
    </canvas>
    
    <!-- 显示转换后的图片 -->
    <image
      v-show="qrCodeImageSrc"
      mode="aspectFit"
      :src="qrCodeImageSrc"
      style="width: 300rpx; height: 300rpx"
    />
    
    <!-- 加载状态 -->
    <view v-show="isGeneratingQR" class="qr-loading">
      <uni-icons type="spinner-cycle" size="40" color="#1890ff"></uni-icons>
      <text class="loading-text">生成中...</text>
    </view>
  </view>
</template>
```

#### 2. 脚本逻辑

```javascript
import { ref, onMounted, nextTick, getCurrentInstance } from 'vue'
import { generateQRCodeImage } from '@/utils/qrcode.js'

// 状态管理
const qrCodeImageSrc = ref('')
const isGeneratingQR = ref(false)
const canvasId = ref(`qr-canvas-${Date.now()}`)

// 生成二维码
const generateQRCode = async () => {
  if (!orderData.orderNo || qrCodeImageSrc.value) return
  
  isGeneratingQR.value = true
  
  try {
    await nextTick()
    
    const tempFilePath = await generateQRCodeImage({
      text: orderData.orderNo,
      canvasId: canvasId.value,
      size: 300,
      componentInstance: getCurrentInstance()
    })
    
    qrCodeImageSrc.value = tempFilePath
    isGeneratingQR.value = false
  } catch (error) {
    console.error('二维码生成失败:', error)
    isGeneratingQR.value = false
  }
}

onMounted(() => {
  generateQRCode()
})
```

#### 3. 样式设置

```scss
.qr-code {
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  canvas {
    position: absolute;
    top: -1000rpx; // 隐藏canvas，避免层级问题
    left: -1000rpx;
  }

  image {
    border-radius: 8rpx;
  }

  .qr-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .loading-text {
      font-size: 24rpx;
      color: #1890ff;
      margin-top: 16rpx;
    }
  }
}
```

### 关键要点

#### 1. 延迟转换

```javascript
ctx.draw(false, () => {
  // 关键：必须延迟转换，等待canvas绘制完成
  setTimeout(() => {
    uni.canvasToTempFilePath({
      canvasId: canvasId,
      success: (res) => {
        qrCodeImageSrc.value = res.tempFilePath
      },
      fail: (err) => {
        console.error('转换失败:', err)
      }
    }, componentInstance) // 关键：传入组件实例
  }, 1000)
})
```

#### 2. 组件实例传递

在组件中使用时，必须传入第二个参数（组件实例）：

```javascript
uni.canvasToTempFilePath(options, getCurrentInstance())
```

#### 3. Canvas隐藏

通过CSS将canvas移出可视区域，而不是使用`display: none`：

```css
canvas {
  position: absolute;
  top: -1000rpx;
  left: -1000rpx;
}
```

## 工具函数

创建了专门的二维码生成工具 `utils/qrcode.js`，提供：

- `generateQRCodeImage()` - 生成二维码图片
- 简化的二维码绘制算法
- 错误处理和状态管理

## 使用示例

```javascript
import { generateQRCodeImage } from '@/utils/qrcode.js'

// 生成订单二维码
const qrImage = await generateQRCodeImage({
  text: orderNo,
  canvasId: 'my-qr-canvas',
  size: 300,
  componentInstance: getCurrentInstance()
})
```

## 注意事项

1. **延迟时间**：canvas绘制需要时间，建议延迟1000ms后再转换
2. **组件实例**：在组件中使用时必须传入组件实例
3. **Canvas ID**：确保每个canvas有唯一的ID
4. **错误处理**：添加适当的错误处理和用户提示
5. **专业库**：生产环境建议使用专业的二维码库如 `weapp-qrcode`

## 参考资料

- [原始解决方案博客](https://blog.csdn.net/qq_44741577/article/details/149716555)
- [uniapp canvas文档](https://uniapp.dcloud.io/api/canvas/canvasToTempFilePath)
- [uniapp原生组件说明](https://uniapp.dcloud.io/component/native-component)
