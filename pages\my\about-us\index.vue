<template>
	<view class="container">
		<!-- 内容区域 -->
		<scroll-view class="content" scroll-y>
			<!-- 公司概况 -->
			<view class="section">
				<view class="section-title">公司概况</view>
				<view class="section-content">
					<text class="company-info">
						熙桓（北京）心理咨询有限公司成立于2020年12月28日，
						注册资本222.222222万元人民币，实缴资本4万元人民币，
						公司由熙桓集团有限公司董事兼总裁、熙桓科技董事、形成
						核心管理团队。截至2023年，企业规模为预算。
					</text>
				</view>

				<!-- 公司图片 -->
				<view class="company-image">
					<image src="../../../static/logo.png" mode="aspectFit"></image>
					<view class="image-tag">公司标识</view>
				</view>
				<view class="section-title">业务范围</view>
				<view class="section-content">
					<text class="business-info">
						公司主营业务涵盖心理咨询、健康管理及企业心理咨询中心全
						方位为客户、企业心理咨询中心提供专业和企业心理咨询中心全
						理解决方案。同时覆盖市场调查、企业管理咨询等技术服务
						运营咨询等领域的"公众企业心理咨询中心及其他专业化服
						务。企事业单位提供专业化方案服务。
					</text>
				</view>
				<view class="section-title">知识产权</view>
				<view class="section-content">
					<text class="ip-info">
						截至2025年4月，公司拥有10项商标信息及1个行政许可，
						主要涉及心理咨询商务、2022年1月19日完成注册地址变更
						等。企业信用信息代码为91110113MADYFBN24。
					</text>
				</view>

				<view class="section-title">联系我们</view>
				<view class="contact-info">
					<view class="contact-item">
						<view class="contact-label">客服电话：</view>
						<view class="contact-value" @click="makePhoneCall">010-12342332</view>
					</view>
					<view class="contact-item">
						<view class="contact-label">公司地址：</view>
						<view class="contact-value">北京市朝阳区XXX街道XXX号</view>
					</view>
					<view class="contact-item">
						<view class="contact-label">邮箱地址：</view>
						<view class="contact-value"><EMAIL></view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { ref } from 'vue'

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 拨打客服电话
const makePhoneCall = () => {
	uni.makePhoneCall({
		phoneNumber: '010-12342332',
		success: () => {
			console.log('拨打电话成功')
		},
		fail: (err) => {
			console.error('拨打电话失败:', err)
			uni.showToast({
				title: '拨打失败',
				icon: 'none'
			})
		}
	})
}
</script>

<style scoped lang="scss">
.container {
	height: 100vh;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}

.navbar {
	height: 88rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
	border-bottom: 1rpx solid #eee;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;

	.nav-left {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.nav-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
	}

	.nav-right {
		display: flex;
		align-items: center;
		gap: 20rpx;

		.record-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}

.content {
	flex: 1;
	padding: 32rpx;
	width: calc(100% - 64rpx);
	height: calc(100vh - 64rpx);
}

.section {
	background-color: #fff;
	margin-bottom: 24rpx;

	.section-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #000;
		margin-bottom: 22rpx;
	}

	.section-content {
		margin-bottom: 31rpx;

		.company-info,
		.business-info,
		.ip-info {
			font-size: 26rpx;
			line-height: 1.6;
			color: #8A8788;
			text-align: justify;
		}
	}
}

.company-image {
	position: relative;
	width: 100%;
	height: 388rpx;
	border-radius: 12rpx;
	overflow: hidden;
	margin-bottom: 40rpx;

	image {
		width: 100%;
		height: 100%;
	}

	.image-tag {
		position: absolute;
		bottom: 16rpx;
		right: 16rpx;
		background: rgba(0, 0, 0, 0.6);
		color: #fff;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
	}
}

.contact-info {
	.contact-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.contact-label {
			font-size: 28rpx;
			color: #666;
			width: 160rpx;
		}

		.contact-value {
			font-size: 28rpx;
			color: #333;
			flex: 1;

			&:first-child {
				color: #B85A8A;
			}
		}
	}
}
</style>
