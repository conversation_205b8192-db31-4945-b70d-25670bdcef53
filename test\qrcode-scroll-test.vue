<template>
  <view class="test-container">
    <uni-nav-bar title="二维码滚动测试" left-icon="back" @clickLeft="goBack" />
    
    <scroll-view scroll-y class="scroll-container">
      <!-- 顶部内容 -->
      <view class="content-section">
        <text class="section-title">顶部内容区域</text>
        <view class="content-item" v-for="i in 5" :key="i">
          <text>这是第 {{ i }} 个内容项</text>
        </view>
      </view>

      <!-- 二维码测试区域 -->
      <view class="qr-test-section">
        <text class="section-title">二维码测试区域</text>
        
        <!-- 原始canvas方式（会有层级问题） -->
        <view class="test-item">
          <text class="test-label">原始Canvas（有层级问题）:</text>
          <view class="qr-container">
            <canvas 
              canvas-id="original-canvas" 
              id="original-canvas"
              style="width: 200rpx; height: 200rpx; border: 1px solid #ddd;"
            ></canvas>
          </view>
        </view>

        <!-- 修复后的方式（转换为图片） -->
        <view class="test-item">
          <text class="test-label">修复后方式（转换为图片）:</text>
          <view class="qr-container">
            <!-- 隐藏的canvas -->
            <canvas 
              v-show="!fixedQRImage"
              canvas-id="fixed-canvas" 
              id="fixed-canvas"
              style="width: 200rpx; height: 200rpx;"
            ></canvas>
            
            <!-- 显示的图片 -->
            <image 
              v-show="fixedQRImage"
              :src="fixedQRImage" 
              mode="aspectFit"
              style="width: 200rpx; height: 200rpx; border: 1px solid #ddd;"
            />
            
            <!-- 加载状态 -->
            <view v-show="isGenerating" class="loading">
              <text>生成中...</text>
            </view>
          </view>
        </view>

        <!-- 使用组件的方式 -->
        <view class="test-item">
          <text class="test-label">使用UniversalOrderDetail组件:</text>
          <UniversalOrderDetail 
            :orderData="testOrderData"
            :qrCodeDesc="'测试二维码，请勿泄露'"
          />
        </view>
      </view>

      <!-- 中间内容 -->
      <view class="content-section">
        <text class="section-title">中间内容区域</text>
        <view class="content-item" v-for="i in 10" :key="`mid-${i}`">
          <text>中间内容项 {{ i }}</text>
        </view>
      </view>

      <!-- 底部内容 -->
      <view class="content-section">
        <text class="section-title">底部内容区域</text>
        <view class="content-item" v-for="i in 5" :key="`bottom-${i}`">
          <text>底部内容项 {{ i }}</text>
        </view>
      </view>

      <!-- 固定在底部的元素（用于测试层级） -->
      <view class="fixed-bottom">
        <text>固定底部元素 - 测试层级</text>
      </view>
    </scroll-view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button @click="generateOriginalQR" class="btn">生成原始二维码</button>
      <button @click="generateFixedQR" class="btn">生成修复二维码</button>
      <button @click="refreshTest" class="btn">刷新测试</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import UniversalOrderDetail from '@/components/UniversalOrderDetail/UniversalOrderDetail.vue'
import { generateQRCodeImage } from '@/utils/qrcode.js'
import { ORDER_STATUS } from '@/api/payment.js'

// 状态管理
const fixedQRImage = ref('')
const isGenerating = ref(false)

// 测试订单数据
const testOrderData = ref({
  orderNo: 'TEST' + Date.now(),
  status: ORDER_STATUS.PAID,
  orderType: 'consultant',
  productName: '心理咨询服务',
  productImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
  price: 299.00,
  createTime: new Date().toISOString(),
  payTime: new Date().toISOString(),
  userPhone: '138****8888'
})

// 生成原始二维码（有层级问题）
const generateOriginalQR = () => {
  const ctx = uni.createCanvasContext('original-canvas', getCurrentInstance())
  
  // 简单绘制
  ctx.setFillStyle('#ffffff')
  ctx.fillRect(0, 0, 200, 200)
  
  ctx.setFillStyle('#000000')
  ctx.fillRect(20, 20, 160, 160)
  
  ctx.setFillStyle('#ffffff')
  ctx.fillRect(40, 40, 120, 120)
  
  ctx.setFillStyle('#000000')
  ctx.setFontSize(12)
  ctx.setTextAlign('center')
  ctx.fillText('原始Canvas', 100, 100)
  
  ctx.draw()
}

// 生成修复后的二维码
const generateFixedQR = async () => {
  if (isGenerating.value) return
  
  isGenerating.value = true
  fixedQRImage.value = ''
  
  try {
    const tempFilePath = await generateQRCodeImage({
      text: 'FIXED-QR-' + Date.now(),
      canvasId: 'fixed-canvas',
      size: 200,
      componentInstance: getCurrentInstance()
    })
    
    fixedQRImage.value = tempFilePath
  } catch (error) {
    console.error('生成失败:', error)
    uni.showToast({
      title: '生成失败',
      icon: 'none'
    })
  } finally {
    isGenerating.value = false
  }
}

// 刷新测试
const refreshTest = () => {
  fixedQRImage.value = ''
  testOrderData.value.orderNo = 'TEST' + Date.now()
  
  // 重新生成
  setTimeout(() => {
    generateOriginalQR()
    generateFixedQR()
  }, 100)
}

// 返回
const goBack = () => {
  uni.navigateBack()
}

// 页面加载时生成二维码
onMounted(() => {
  setTimeout(() => {
    generateOriginalQR()
    generateFixedQR()
  }, 500)
})
</script>

<style lang="scss" scoped>
.test-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  background: #f5f5f6;
}

.content-section {
  background: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }

  .content-item {
    padding: 20rpx 0;
    border-bottom: 1rpx solid #eee;

    &:last-child {
      border-bottom: none;
    }
  }
}

.qr-test-section {
  background: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
    display: block;
  }

  .test-item {
    margin-bottom: 40rpx;
    
    .test-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 20rpx;
      display: block;
    }

    .qr-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200rpx;
      position: relative;

      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #1890ff;
      }
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  z-index: 999;
}

.action-buttons {
  display: flex;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #eee;

  .btn {
    flex: 1;
    margin: 0 10rpx;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}
</style>
