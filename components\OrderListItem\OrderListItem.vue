<template>
	<view class="order-item" @click="handleClick">
		<view class="top-row">
			<!-- 左侧图片 -->
			<view class="item-left">
				<image class="avatar" mode="aspectFill" :src="getImageUrl()" @error="handleImageError"></image>
			</view>

			<!-- 右侧内容 -->
			<view class="item-right">
				<!-- 标题和状态 -->
				<view class="title-row">
					<view class="title-box">
						<view class="name">{{ getDisplayName() }}</view>
						<view v-if="getGradeText" class="grade">{{ getGradeText }}</view>
					</view>
					<view class="status" :class="getStatusClass()">{{ getStatusText() }}</view>
				</view>

				<!-- 标签行 -->
				<view class="tags-row">
					<!-- 咨询订单显示图标 -->
					<view v-if="props.type === 'consultant'" class="consultation-icons">
						<image v-if="props.item.consultationType === '到店咨询'" src="../../static/icon/my/咨询方式@2x.png"></image>
						<image v-else src="../../static/icon/my/组 <EMAIL>"></image>
					</view>
					<!-- 其他类型显示标签 -->
					<view v-else class="tag" v-for="tag in getTags()" :key="tag.text" :class="tag.type">
						{{ tag.text }}
					</view>
				</view>

				<!-- 时间信息 -->
				<view class="time-info">{{ getTimeInfo() }}</view>

			</view>
		</view>
		<!-- 底部操作区 -->
		<view class="bottom-row">
			<view class="price-info">
				<text class="price-label">实付：</text>
				<text class="price">¥ {{ getActualPrice() }}</text>
			</view>
			<view class="action-buttons">
				<button v-for="button in getActionButtons()" :key="button.text" class="action-btn" :class="button.type"
					@click.stop="handleButtonClick(button)">
					{{ button.text }}
				</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
	item: {
		type: Object,
		required: true
	},
	type: {
		type: String,
		default: 'consultant' // 'consultant', 'assessment', 'meditation', 'course'
	},
	dictData: {
		type: Object,
		default: () => ({})
	}
})

// Emits
const emit = defineEmits(['click', 'action'])

// 默认图片
const defaultImages = {
	consultant: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
	meditation: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
	course: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
	assessment: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
}

// 获取图片URL
const getImageUrl = () => {
	if (props.type === 'consultant') {
		return props.item.consultantAvatar || props.item.avatar || defaultImages.consultant
	} else if (props.type === 'assessment') {
		return props.item.assessmentCover || props.item.cover || defaultImages.assessment
	} else if (props.type === 'meditation') {
		return props.item.meditationCover || props.item.cover || defaultImages.meditation
	} else if (props.type === 'course') {
		return props.item.courseCover || props.item.cover || defaultImages.course
	}
	return defaultImages[props.type]
}

// 处理图片加载错误
const handleImageError = (e) => {
	e.target.src = defaultImages[props.type]
}

// 获取显示名称
const getDisplayName = () => {
	if (props.type === 'consultant') {
		return props.item.consultantName || props.item.name || '咨询师'
	} else if (props.type === 'assessment') {
		return props.item.assessmentName || props.item.name || '测评'
	} else if (props.type === 'meditation') {
		return props.item.meditationName || props.item.name || '冥想'
	} else if (props.type === 'course') {
		return props.item.courseName || props.item.name || '课程'
	}
	return props.item.name || '未知'
}

// 获取状态文本
const getStatusText = () => {
	const statusMap = {
		'pending': '待使用',
		'in_progress': '进行中',
		'completed': '已完成',
		'refunding': '退款中',
		'refunded': '已退款',
		'cancelled': '已取消'
	}
	return statusMap[props.item.status] || '未知状态'
}

// 获取状态样式类
const getStatusClass = () => {
	const classMap = {
		'pending': 'status-pending',
		'in_progress': 'status-progress',
		'completed': 'status-completed',
		'refunding': 'status-refunding',
		'refunded': 'status-refunded',
		'cancelled': 'status-cancelled'
	}
	return classMap[props.item.status] || 'status-default'
}

// 获取标签
const getTags = () => {
	const tags = []

	if (props.type === 'consultant') {
		tags.push({ text: '咨询方式', type: 'label' })
		if (props.item.consultationType) {
			tags.push({ text: props.item.consultationType, type: 'value' })
		}
	} else if (props.type === 'assessment') {
		tags.push({ text: '测评', type: 'label' })
		if (props.item.assessmentType) {
			tags.push({ text: props.item.assessmentType, type: 'value' })
		}
	} else if (props.type === 'meditation') {
		tags.push({ text: '冥想', type: 'label' })
		if (props.item.duration) {
			tags.push({ text: `${props.item.duration}分钟`, type: 'value' })
		}
	} else if (props.type === 'course') {
		tags.push({ text: '课程', type: 'label' })
		if (props.item.courseType) {
			tags.push({ text: props.item.courseType, type: 'value' })
		}
	}

	return tags
}


// 获取等级文本 - 使用计算属性确保响应式更新
const getGradeText = computed(() => {
	if (props.type === 'consultant') {
		// 获取咨询师等级，支持多种字段名
		const level = props.item.counselorLevel ||
			props.item.level ||
			props.item.consultant?.personalTitle ||
			props.item.personalTitle ||
			props.item.consultant?.counselorLevel ||
			props.item.consultant?.level
		return getPsy_consultant_level(level)
	}

	if (props.type === 'assessment') {
		// 测评显示适用年龄
		const age = props.item.assessment?.applicableAge || props.item.applicableAge
		if (age) return age
	}

	if (props.type === 'course') {
		// 课程显示难度等级
		const difficulty = props.item.course?.difficulty || props.item.difficulty
		if (difficulty) return difficulty
	}

	// 其他类型的等级标识
	if (props.item.level) return props.item.level
	if (props.item.difficulty) return props.item.difficulty
	if (props.item.grade) return props.item.grade

	return ''
})


// 咨询师等级转换函数
const getPsy_consultant_level = (level) => {
	// 如果 level 为空或未定义，返回空字符串
	if (level === undefined || level === null || level === '') {
		return ''
	}

	// 优先使用传入的字典数据
	if (props.dictData && props.dictData.psy_consultant_level && Array.isArray(props.dictData.psy_consultant_level)) {
		const found = props.dictData.psy_consultant_level.find((item) => item.dictValue == level)
		if (found?.dictLabel) {
			return found.dictLabel
		}
	}

	// 降级处理：使用默认映射（根据您提供的字典数据）
	const levelMap = {
		'0': '咨询助理',
		'1': '初级咨询师',
		'2': '中级咨询师',
		'3': '成熟咨询师',
		'4': '高级咨询师',
		'5': '资深咨询师',
		'6': '咨询督导'
	}

	// 转换为字符串进行匹配
	const levelStr = String(level)
	return levelMap[levelStr] || levelStr || ''
}


// 获取时间信息
const getTimeInfo = () => {
	if (props.type === 'consultant') {
		// 咨询显示预约时间
		return props.item.appointmentTime || '07/08 14:00-15:00'
	} else if (props.type === 'course') {
		// 课程显示章节数
		return props.item.chapterCount ? `${props.item.chapterCount}章节` : '12章节'
	} else if (props.type === 'assessment') {
		// 测评显示预估完成时间
		return props.item.estimatedTime ? `预估${props.item.estimatedTime}分钟完成` : '预估15分钟完成'
	} else if (props.type === 'meditation') {
		// 冥想显示时长
		return props.item.duration ? `时长：${props.item.duration}min` : '时长：15min'
	}

	return props.item.createTime || '07/08 14:00'
}

// 获取实际价格
const getActualPrice = () => {
	return props.item.actualPrice || props.item.price || '29.9'
}

// 获取操作按钮
const getActionButtons = () => {
	const status = props.item.status
	const buttons = []

	if (props.type === 'assessment') {
		// 测评类型的按钮逻辑
		switch (status) {
			case 'pending':
				buttons.push({ text: '去测评', type: 'primary', action: 'start' })
				break
			case 'in_progress':
				buttons.push({ text: '继续', type: 'primary', action: 'continue' })
				break
			case 'completed':
				buttons.push({ text: '查看结果', type: 'primary', action: 'view_result' })
				break
			case 'refunding':
				buttons.push({ text: '详情', type: 'default', action: 'detail' })
				break
			default:
				buttons.push({ text: '详情', type: 'default', action: 'detail' })
				break
		}
	} else if (props.type === 'course') {
		// 课程类型的按钮逻辑
		switch (status) {
			case 'pending':
				buttons.push({ text: '开始学习', type: 'primary', action: 'use' })
				break
			case 'in_progress':
				buttons.push({ text: '继续学习', type: 'primary', action: 'continue' })
				break
			case 'completed':
				buttons.push({ text: '已完成', type: 'completed', action: 'detail' })
				break
			case 'refunding':
				buttons.push({ text: '详情', type: 'default', action: 'detail' })
				break
			default:
				buttons.push({ text: '详情', type: 'default', action: 'detail' })
				break
		}
	} else if (props.type === 'meditation') {
		// 冥想类型的按钮逻辑
		switch (status) {
			case 'pending':
				buttons.push({ text: '开始冥想', type: 'primary', action: 'use' })
				break
			case 'in_progress':
				buttons.push({ text: '继续', type: 'primary', action: 'continue' })
				break
			case 'completed':
				buttons.push({ text: '已完成', type: 'completed', action: 'detail' })
				break
			case 'refunding':
				buttons.push({ text: '详情', type: 'default', action: 'detail' })
				break
			default:
				buttons.push({ text: '详情', type: 'default', action: 'detail' })
				break
		}
	} else {
		// 其他类型的按钮逻辑（咨询、冥想、课程等）
		switch (status) {
			case 'pending':
				if (props.type === 'consultant') {
					buttons.push({ text: '去咨询', type: 'primary', action: 'consult' })
				} else {
					buttons.push({ text: '去使用', type: 'primary', action: 'use' })
				}
				break
			case 'in_progress':
				if (props.type === 'consultant') {
					buttons.push({ text: '查看劵码', type: 'primary', action: 'view' })
				} else {
					buttons.push({ text: '继续', type: 'primary', action: 'continue' })
				}
				break
			case 'completed':
				buttons.push({ text: '已核销', type: 'completed', action: 'detail' })
				break
			case 'refunding':
				buttons.push({ text: '详情', type: 'default', action: 'detail' })
				break
			default:
				buttons.push({ text: '详情', type: 'default', action: 'detail' })
				break
		}
	}

	return buttons
}

// 处理点击事件
const handleClick = () => {
	emit('click', props.item)
}

// 处理按钮点击
const handleButtonClick = (button) => {
	emit('action', { action: button.action, item: props.item })
}
</script>

<style lang="scss" scoped>
.order-item {
	width: calc(100% - 62rpx);
	padding: 31rpx;
	background-color: #fff;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

	.top-row {
		display: flex;
		border-bottom: 2rpx solid #E7E7E7;
	}

	.item-left {
		width: 98rpx;
		height: 98rpx;
		margin-right: 16rpx;
		flex-shrink: 0;

		.avatar {
			width: 100%;
			height: 100%;
			border-radius: 12rpx;
		}
	}

	.item-right {
		flex: 1;
		display: flex;
		flex-direction: column;

		.title-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;

			.title-box {
				display: flex;
				align-items: center;
			}

			.name {
				font-size: 28rpx;
				font-weight: 700;
				color: #000;
				flex: 1;
			}

			.grade {
				font-size: 20rpx;
				background-color: #F7F7F7;
				padding: 6rpx 8rpx;
				color: #8A8788;
				border-radius: 4rpx;
				white-space: nowrap;
			}

			.status {
				font-size: 28rpx;

				&.status-pending {
					color: #455DA0;
				}

				&.status-progress {
					color: #A04571;
				}

				&.status-completed {
					color: #000000;
				}

				&.status-refunding {
					color: #000000;
				}
			}
		}

		.tags-row {
			display: flex;
			margin-bottom: 15rpx;

			.tag {
				font-size: 20rpx;
				padding: 7rpx 11rpx;
				border-radius: 4rpx;
				margin-right: 12rpx;
				background-color: #F7F7F7;
				color: #8A8788;
			}

			.consultation-icons {
				display: flex;
				align-items: center;

				image {
					width: 198rpx;
					height: 34rpx;
				}
			}
		}

		.time-info {
			font-size: 20rpx;
			color: #ACA8AA;
			margin-bottom: 16rpx;
		}

	}

	.bottom-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 24rpx;

		.price-info {
			display: flex;
			align-items: baseline;

			.price-label {
				font-size: 24rpx;
				color: #8A8788;
			}

			.price {
				font-size: 24rpx;
				color: #8A8788;
			}
		}

		.action-buttons {
			display: flex;
			gap: 12rpx;
			height: 56rpx;

			.action-btn {
				padding: 0 27rpx;
				border-radius: 12rpx;
				font-size: 24rpx;
				border: none;

				&.primary {
					background: linear-gradient(to right, #A04571 0%, #923C65 100%);
					color: #fff;
				}

				&.default {
					background-color: #F5F5F5;
					color: #ACA8AA;

				}

				&.completed {
					background: #F6F1F4;
					color: #A04571;
				}

				&::after {
					border: none;
				}
			}
		}
	}
}
</style>
