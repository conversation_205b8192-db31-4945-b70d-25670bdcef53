<template>
	<view class="match-result">
		<view class="success-tip">
			<view class="tip-text">根据您的问答匹配到以下咨询师</view>
		</view>

		<scroll-view class="counselor-container" scroll-x="true" show-scrollbar="false" :enhanced="true" :bounces="false"
			@scroll="handleScroll">
			<view class="counselor-wrapper">
				<view class="counselor-card" v-for="(item, index) in counselorList" :key="index">

					<!-- 咨询师头像和基本信息 -->
					<view class="counselor-header">
						<image class="counselor-avatar" :src="item.imageUrl" mode="aspectFill"></image>
						<view class="counselor-info">
							<view class="name-section">
								<view>
									<text class="name">{{ item.name }}</text>
									<text class="title">{{ getTitleText(item.personalTitle) }}</text>
								</view>
								<view class="profile-btn" @click="handleViewDetailWithCounselor(item)">
									<text>个人主页</text>
								</view>
							</view>
							<view class="stats-section">
								<view class="stat-item">
									<text class="stat-number">{{ item.workYears || '15' }}年</text>
									<text class="stat-label">从业经验</text>
								</view>
								<view class="stat-item">
									<text class="stat-number">{{ item.consultCount || '1000' }}+</text>
									<text class="stat-label">咨询案例</text>
								</view>
							</view>
							<view class="tags-section" v-if="item.expertises && item.expertises.length">
								<text v-for="(expertise, idx) in item.expertises.slice(0, 3)" :key="idx" class="expertise-tag">
									{{ expertise.typeName }}
								</text>
							</view>
						</view>
					</view>

					<!-- 个人介绍 -->
					<view class="intro-section">
						<view class="section-title">个人介绍</view>
						<text class="intro-text">{{ item.introduction ||
							'国家二级心理咨询师，四川师范大学研究院咨询师，擅长心理研究，从业15年，临床心理学硕士，拥有较丰富的沙盘游戏治疗和谈话治疗的临床工作经验。' }}</text>
					</view>

					<!-- 匹配度雷达图 -->
					<view class="match-section">
						<view class="section-title">匹配度</view>
						<view class="radar-chart-container">
							<!-- <l-echart :ref="el => setChartRef(el, index)" class="radar-chart" :disable-scroll="true">
							</l-echart> -->
						</view>
					</view>

					<!-- 匹配理由 -->
					<view class="reason-section">
						<view class="section-title">匹配理由</view>
						<text class="reason-text">{{ item.matchReason ||
							'国家二级心理咨询师，四川师范大学研究院咨询师，擅长心理研究，从业15年，临床心理学硕士，拥有较丰富的沙盘游戏治疗和谈话治疗的临床工作经验。' }}</text>
					</view>

					<!-- 价格和预约按钮 -->
					<view class="booking-section">
						<view class="price-info">
							<text class="price">¥{{ item.price || '29.9' }}</text>
							<text class="unit">/次</text>
							<text class="original-price">¥{{ item.originalPrice || '138.0' }}</text>
						</view>
						<button class="booking-btn" @click="handleMakeAppointmentWithCounselor(item)">
							立即预约
						</button>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- <view class="swipe-tip" v-if="counselorList.length > 1">
			<text class="tip-text">左右滑动查看更多咨询师</text>
		</view> -->


		<!-- 滑动指示器 -->
		<!-- <view class="swipe-indicators" v-if="counselorList.length > 1">
			<view class="indicator" v-for="(_, index) in counselorList" :key="index"
				:class="{ 'active': index === currentIndex }">
			</view>
		</view> -->
	</view>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';

// 小程序使用 require 导入 echarts
// #ifdef MP
const echarts = require('../../uni_modules/lime-echart/static/echarts.min');
// #endif
// #ifndef MP
import * as echarts from 'echarts/core';
import { RadarChart } from 'echarts/charts';
import { TitleComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([RadarChart, TitleComponent, LegendComponent, CanvasRenderer]);
// #endif

// 咨询师数据
const allCounselors = ref([]);
const currentIndex = ref(0);

// 当前显示的咨询师列表（最多5个）
const counselorList = computed(() => {
	return allCounselors.value.slice(0, 5);
});

// 获取职称文本
const getTitleText = (titleCode) => {
	const titles = {
		'1': '初级心理咨询师',
		'2': '中级心理咨询师',
		'3': '高级心理咨询师',
		'4': '资深心理咨询师'
	};
	return titles[titleCode] || '心理咨询师';
};

// 处理滚动事件
const handleScroll = (e) => {
	const scrollLeft = e.detail.scrollLeft;
	// 使用新的 API 获取窗口信息
	const windowInfo = uni.getWindowInfo();
	const cardWidth = windowInfo.windowWidth - 140; // 卡片宽度
	const newIndex = Math.round(scrollLeft / (cardWidth + 20)); // 20是间距

	if (newIndex !== currentIndex.value && newIndex >= 0 && newIndex < counselorList.value.length) {
		currentIndex.value = newIndex;
		// 只更新当前索引，不重新初始化图表
	}
};



// 查看详情并传递咨询师信息
const handleViewDetailWithCounselor = (counselor) => {
	uni.navigateTo({
		url: `/pages/classification/counselor-detail/index?id=${counselor.id}`
	});
};

// 立即预约并传递咨询师信息
const handleMakeAppointmentWithCounselor = (counselor) => {
	uni.navigateTo({
		url: `/pages/appointment/index?counselorId=${counselor.id}`
	});
};

// 存储图表 ref 引用
const chartRefs = ref({});

// 设置图表 ref
const setChartRef = (el, index) => {
	if (el) {
		chartRefs.value[`chartRef${index}`] = el;
	}
};

// 初始化雷达图
const initRadarChart = async (index) => {
	try {
		// 获取图表 ref
		const chartRef = chartRefs.value[`chartRef${index}`];

		if (!chartRef) {
			console.warn(`图表 ref chartRef${index} 未找到`);
			return;
		}

		// 等待组件渲染完成
		await nextTick();

		// 初始化图表
		const myChart = await chartRef.init(echarts);

		const counselor = counselorList.value[index];
		const option = {
			radar: {
				indicator: [
					{ name: '基本信息', max: 100 },
					{ name: '性格风格', max: 100 },
					{ name: '咨询结构', max: 100 },
					{ name: '咨询方式', max: 100 },
					{ name: '理论去向', max: 100 }
				],
				radius: '70%',
				splitNumber: 4,
				axisLine: {
					lineStyle: {
						color: '#E8E8E8',
						width: 1
					}
				},
				splitLine: {
					lineStyle: {
						color: '#E8E8E8',
						width: 1
					}
				},
				splitArea: {
					show: true,
					areaStyle: {
						color: ['rgba(250, 250, 250, 0.3)', 'rgba(245, 245, 245, 0.3)']
					}
				},
				name: {
					textStyle: {
						color: '#666',
						fontSize: 11
					}
				}
			},
			series: [{
				type: 'radar',
				data: [{
					value: [
						counselor?.matchRate || 98,
						100,
						100,
						100,
						96
					],
					name: '匹配度',
					areaStyle: {
						color: 'rgba(160, 69, 113, 0.2)'
					},
					lineStyle: {
						color: '#A04571',
						width: 2
					},
					itemStyle: {
						color: '#A04571',
						borderColor: '#A04571',
						borderWidth: 2
					}
				}]
			}]
		};

		myChart.setOption(option);
		console.log(`雷达图 ${index} 初始化完成`);

	} catch (error) {
		console.error(`雷达图 ${index} 初始化失败:`, error);
	}
};

// 初始化所有雷达图
const initAllRadarCharts = async () => {
	console.log('开始初始化所有雷达图...');

	// 等待所有组件渲染完成
	await nextTick();

	// 延迟一段时间确保所有 ref 都已设置
	await new Promise(resolve => setTimeout(resolve, 1000));

	// 初始化每个雷达图
	for (let i = 0; i < counselorList.value.length; i++) {
		await initRadarChart(i);
		// 每个图表之间稍微延迟，避免同时初始化造成性能问题
		await new Promise(resolve => setTimeout(resolve, 200));
	}

	console.log('所有雷达图初始化完成');
};

// 初始化数据
const initData = () => {
	const matchedConsultants = uni.getStorageSync('matchedConsultants');
	if (matchedConsultants && matchedConsultants.length > 0) {
		const baseMatchRate = 98;
		allCounselors.value = matchedConsultants.map((item, index) => {
			const reduction = index === 0 ? 0 : Math.floor(Math.random() * 10) + 1;
			return {
				...item,
				matchRate: baseMatchRate - reduction
			};
		});
		allCounselors.value.sort((a, b) => b.matchRate - a.matchRate);

		// 初始化所有雷达图
		setTimeout(() => {
			initAllRadarCharts();
		}, 800);
	} else {
		uni.showToast({
			title: '未找到匹配的咨询师',
			icon: 'none'
		});
		setTimeout(() => {
			uni.navigateBack();
		}, 1500);
	}
};

onMounted(() => {
	initData();
});
</script>

<style lang="scss" scoped>
.match-result {
	min-height: 100vh;
	background: #fff;
	padding: 20rpx 0 20rpx 20rpx; // 右边不要内边距

	.success-tip {
		margin-bottom: 30rpx;
		padding-top: 20rpx;
		padding-right: 20rpx; // 补充右边距

		.tip-text {
			font-size: 32rpx;
			color: #000;
			font-weight: 500;
		}
	}

	.counselor-container {
		height: calc(100vh - 180rpx);
		white-space: nowrap;
		background-color: #f5f5f5;
		// 优化滚动性能
		-webkit-overflow-scrolling: touch;
	}

	.counselor-wrapper {
		display: inline-flex;
		height: 100%;
		background-color: #fff;
	}

	.counselor-card {
		display: inline-block;
		width: calc(100vw - 140rpx); // 卡片宽度，右侧露出120rpx
		margin-right: 20rpx; // 卡片间距
		border-radius: 16rpx;
		overflow: hidden;
		vertical-align: top;
		white-space: normal;
		background-color: #f5f5f5;
		padding: 24rpx;

		// 咨询师头像和基本信息
		.counselor-header {
			display: flex;
			align-items: flex-start;
			position: relative;

			.counselor-avatar {
				width: 100rpx;
				height: 100rpx;
				margin-right: 16rpx;
				flex-shrink: 0;
			}

			.counselor-info {
				flex: 1;

				.name-section {
					margin-bottom: 16rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.profile-btn {
						font-size: 22rpx;
						color: #8B8788;
					}

					.name {
						font-size: 30rpx;
						font-weight: 500;
						color: #000;
						margin-right: 16rpx;
					}

					.title {
						font-size: 20rpx;
						color: #AD3D72;
						background: #F7F1F4;
						padding: 4rpx 12rpx;
						border-radius: 12rpx;
					}
				}

				.stats-section {
					display: flex;
					gap: 32rpx;
					margin-bottom: 16rpx;

					.stat-item {
						text-align: center;

						.stat-number {
							display: block;
							font-size: 24rpx;
							font-weight: 500;
							color: #000;
							line-height: 1.2;
						}

						.stat-label {
							display: block;
							font-size: 20rpx;
							color: #ADA8AA;
							margin-top: 12rpx;
						}
					}
				}

				.tags-section {
					display: flex;
					flex-wrap: wrap;
					gap: 12rpx;

					.expertise-tag {
						padding: 8rpx 10rpx;
						background: #E7E7E7;
						color: #8B8788;
						border-radius: 4rpx;
						font-size: 20rpx;
					}
				}
			}

		}

		// 个人介绍
		.intro-section {
			padding: 24rpx;
			background: #fff;
			margin-bottom: 24rpx;
			border-radius: 8rpx;

			.section-title {
				font-size: 24rpx;
				color: #000;
				margin-bottom: 16rpx;
			}

			.intro-text {
				font-size: 20rpx;
				color: #8B8788;
				line-height: 1.6;
			}
		}

		// 匹配度雷达图
		.match-section {
			padding: 32rpx 24rpx;
			background: #fff;
			margin-bottom: 24rpx;
			border-radius: 8rpx;

			.section-title {
				font-size: 24cap;
				font-weight: 500;
				color: #000;
				margin-bottom: 16rpx;
			}

			.radar-chart-container {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 20rpx 0;
				// 优化触摸处理，避免阻止父容器滚动
				touch-action: pan-x; // 只允许水平滚动

				.radar-chart {
					width: 280rpx;
					height: 280rpx;
					// 确保图表不会阻止水平滚动
					touch-action: pan-x;
				}
			}
		}

		// 匹配理由
		.reason-section {
			padding: 32rpx 24rpx;
			background: #fff;
			margin-bottom: 24rpx;
			border-radius: 8rpx;

			.section-title {
				font-size: 24rpx;
				font-weight: 500;
				color: #000;
				margin-bottom: 16rpx;
			}

			.reason-text {
				font-size: 20rpx;
				color: #8B8788;
				line-height: 1.6;
			}
		}

		// 价格和预约按钮
		.booking-section {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 32rpx;
			background: #F8F8F8;
			border-top: 1rpx solid #F0F0F0;

			.price-info {
				display: flex;
				align-items: baseline;

				.price {
					font-size: 48rpx;
					font-weight: 600;
					color: #A04571;
				}

				.unit {
					font-size: 28rpx;
					color: #A04571;
					margin-left: 4rpx;
					margin-right: 16rpx;
				}

				.original-price {
					font-size: 28rpx;
					color: #999;
					text-decoration: line-through;
				}
			}

			.booking-btn {
				padding: 16rpx 48rpx;
				background: #A04571;
				color: #fff;
				border: none;
				border-radius: 32rpx;
				font-size: 32rpx;
				font-weight: 500;

				&:active {
					opacity: 0.8;
				}
			}
		}
	}

	.swipe-tip {
		text-align: center;
		margin-top: 20rpx;
		padding-right: 20rpx; // 补充右边距

		.tip-text {
			font-size: 24rpx;
			color: #999;
		}
	}

	.swipe-indicators {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 20rpx;
		gap: 12rpx;
		padding-right: 20rpx; // 补充右边距

		.indicator {
			width: 12rpx;
			height: 12rpx;
			border-radius: 50%;
			background: #E0E0E0;
			transition: all 0.3s ease;

		}
	}
}
</style>
