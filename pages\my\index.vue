<template>
	<view class="content">
		<view class="user-box">
			<!-- <view class="edit-user-info">
				编辑资料
				<uni-icons type="right" size="16"></uni-icons>
			</view> -->
			<view class="user-info" @click="handleToLogin">
				<view class="info">
					<view class="avatar">
						<image :src="avatar" mode=""></image>
					</view>
					<view class="nickname">
						<view class="nickname-box">
							<text class="name-text">{{ userStore.profile.counselorName || nickName }}</text>
							<view style="display: flex; align-items: center;">
								<image class="vip-icon" style="width: 38rpx; height: 34rpx;" src="../../static/icon/my/组 108.png" />
								<text class="vip-text" style="
							font-size: 18rpx;
							color: #A04571;
							padding: 6rpx 10rpx 6rpx 4rpx;
							background-color: #F6F1F4;">会员</text>
							</view>
						</view>
						<view class="phone">186****4411</view>
					</view>
					<view class="user-id" v-if="!userStore.token">登录</view>
					<!-- <view class="is-vip">非会员</view> -->
				</view>
				<view class="settings" @click.stop="handleToSettings">
					<image src="../../static/icon/my/形状 8.png" />
				</view>
				<!-- <view class="vip-code">
					<view class="code">
						<image src="../../static/images/img/会员码.png" mode=""></image>
					</view>
					<text>会员码</text>
				</view> -->
			</view>
			<view class="balance-info">
				<view class="balance-info-box">
					<view class="vip-icon">
						<image src="../../static/icon/my/图层 8.png" />
					</view>
					<view class="vip-info">
						<text class="title">至尊会员卡</text>
						<text class="info">开会员一省再省</text>
					</view>
				</view>
				<button class="confirm-btn" @click="confirmAppointment">立即开通</button>
				<view class="coupon" @click="goToCouponCenter">
					<view class="count">{{ couponCount }}</view>
					<view class="text">优惠券</view>
				</view>
				<view class="balance">
					<view class="count">
						<text class="icon">￥</text>
						<text>0</text>
					</view>
					<view class="text">可用余额</view>
				</view>
				<view class="integral">
					<view class="count">0</view>
					<view class="text">积分</view>
				</view>


			</view>
		</view>
		<view class="order-box">
			<view class="title">我的订单</view>
			<view class="order-box-item">
				<view v-for="item in orderList" :key="item" class="order-content" @click="toOrder(item.url)">
					<image :src="`../../static/icon/my/${item.name}.png`"></image>
					<text>{{ item.name }}</text>
				</view>
			</view>
		</view>
		<view class="option-box">
			<view class="title">更多功能</view>
			<view class="option-box-item">
				<view v-for="item in navigateToList" :key="item.title" @click="handleToUserItem(item)" class="option-item">
					<view class="option-content">
						<image mode="scaleToFill" :src="`../../static/icon/my/${item.title}.png`"></image>
						<text>{{ item.title }}</text>
						<!-- 消息角标 -->
						<view v-if="item.title === '我的消息' && unreadCount > 0" class="message-badge">
							{{ unreadCount > 99 ? '99+' : unreadCount }}
						</view>
					</view>
				</view>
			</view>
		</view>
		<cc-myTabbar :tabBarShow="4"></cc-myTabbar>
	</view>

	<!-- 客服弹框 -->
	<view v-if="showServiceModal" class="service-modal-overlay" @click="closeServiceModal">
		<view class="service-modal" @click.stop>
			<view class="modal-close" @click="closeServiceModal">
				<uni-icons type="close" size="48" color="#fff"></uni-icons>
			</view>
			<view class="service-content">
				<view class="service-icon">
					<image src="../../static/icon/my/组 <EMAIL>"> </image>
				</view>
				<view class="service-title">客服电话</view>
				<view class="service-phone">010-12342332</view>
				<button class="call-btn" @click="makePhoneCall">立即拨号</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { getWXLogin, getInfo } from "../../api/my.js";
import { setToken, getToken, removeToken } from "../../utils/auth.js";
import { useUserStore } from "@/stores/user";
import { useChatStore } from "@/stores/chat";
import { onReady, onShow } from "@dcloudio/uni-app";
const userStore = useUserStore();
const chatStore = useChatStore();

// 响应式数据
const nickName = ref("欢迎来到熙桓心理");
const avatar = ref("../../static/icon/my/图层 6.png");
const unreadCount = ref(0);
const showServiceModal = ref(false);
const couponCount = ref(5); // 优惠券数量
const orderList = ref([
	{
		name: '全部订单',
		url: '/pages/my/my-orders/index'
	},
	{
		name: '待使用',
		url: '/pages/my/my-orders/index?status=pending'
	},
	{
		name: '进行中',
		url: '/pages/my/my-orders/index?status=in_progress'
	},
	{
		name: '已完成',
		url: '/pages/my/my-orders/index?status=completed'
	},
	{
		name: '退款中',
		url: '/pages/my/my-orders/index?status=refunding'
	},
])
// 生命周期
onMounted(() => {
	if (getToken()) {
		avatar.value = userStore.userAvatar || avatar.value;
	}

	// 监听未读消息数量变化
	uni.$on('unread-count-changed', (count) => {
		console.log('我的页面收到未读消息数量变化:', count);
		unreadCount.value = count;
	});

	// 初始化未读消息数量
	loadUnreadCount();
});

onShow(() => {
	// 每次显示页面时刷新未读消息数量
	loadUnreadCount();
});

onReady(() => {
	uni.hideTabBar();
});

// 加载未读消息数量
const loadUnreadCount = async () => {
	if (userStore.userId) {
		await chatStore.getUnreadCount();
		unreadCount.value = chatStore.unreadTotal;
		console.log('我的页面加载未读消息数量:', unreadCount.value);
	}
};

const navigateToList = ref([
	{
		title: "我的测评",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%B8%AA%E4%BA%BA.png",
		url: "/pages/my/my-evaluation/index"
	},
	{
		title: "我的冥想",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/冥想.png",
		url: "/pages/meditation/my-meditations/index"
	},
	{
		title: "我的课程",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/课程.png",
		url: "/pages/course/my-courses/index"
	},
	{
		title: "我的消息",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/tab-icon/%E6%B6%88%E6%81%AF.png",
		url: "/pages/my/my-message/index"
	},
	{
		title: "我的收藏",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%B8%AA%E4%BA%BA.png",
		url: "/pages/my/my-star/index"
	},
	{
		title: "我的客服",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%AE%A2%E6%9C%8D.png",
		url: "/pages/my/my-contact/index"
	},
	{
		title: "关于我们",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E6%A5%BC%E6%88%BF.png",
		url: "/pages/my/about-us/index"
	},
	{
		title: "加入我们",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E6%A5%BC%E6%88%BF.png",
		url: "/pages/my/join-us/index"
	},
	// {
	// 	title: "我的咨询",
	// 	icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/咨询.png",
	// 	url: "/pages/consultation/my-consultations/index"
	// },
	// {
	// 	title: "我的中心",
	// 	icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%B8%AA%E4%BA%BA.png",
	// 	url: "/pages/my/my-detail/index"
	// },
	// {
	// 	title: "帮助中心",
	// 	icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%B8%AE%E5%8A%A9.png",
	// 	url: "/pages/my/my-help/index"
	// },
]);

const toOrder = (url) => {
	uni.navigateTo({ url: url });
}

const handleToUserItem = (item) => {
	// 如果点击的是"我的消息"，清除未读计数
	if (item.title === '我的消息') {
		unreadCount.value = 0;
		// 同时清除store中的未读计数
		chatStore.unreadTotal = 0;
		console.log('点击我的消息，清除未读计数');
	}

	// 如果点击的是"我的客服"，显示弹框
	if (item.title === '我的客服') {
		showServiceModal.value = true;
		return;
	}

	uni.navigateTo({ url: item.url });
};

// 跳转到设置页面
const handleToSettings = () => {
	uni.navigateTo({ url: '/pages/my/settings/index' });
};

// 关闭客服弹框
const closeServiceModal = () => {
	showServiceModal.value = false;
};

// 拨打客服电话
const makePhoneCall = () => {
	uni.makePhoneCall({
		phoneNumber: '010-12342332',
		success: () => {
			console.log('拨打电话成功');
		},
		fail: (err) => {
			console.error('拨打电话失败:', err);
			uni.showToast({
				title: '拨打失败',
				icon: 'none'
			});
		}
	});
};

// 处理登录/个人资料点击
const handleToLogin = () => {
	if (userStore.token) {
		// 已登录，跳转到个人资料页面
		uni.navigateTo({ url: '/pages/my/profile/index' });
	} else {
		// 未登录，跳转到登录页面
		uni.navigateTo({ url: '/pages/login/login' });
	}
};

// 确认开通会员
const confirmAppointment = () => {
	uni.navigateTo({
		url: '/pages/membership/index'
	});
};

// 跳转到领券中心
const goToCouponCenter = () => {
	uni.navigateTo({
		url: '/pages/coupon/index'
	});
};

</script>

<style scoped lang="scss">
.content {
	height: 100vh;
	background-color: #fff;
	padding: 32rpx;
	padding-top: 0;

	.user-box {
		width: 100%;
		margin-top: 40rpx;

		.edit-user-info {
			margin-bottom: 20rpx;
			margin-right: 60rpx;
			text-align: end;
		}

		.user-info {
			width: 100%;
			height: 150rpx;
			display: flex;
			align-items: center;
			margin-bottom: 40rpx;
			justify-content: space-between;

			.avatar {
				width: 100rpx;
				height: 100rpx;
				margin-right: 16rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}

				button {
					width: 150rpx;
					height: 150rpx;
					border-radius: 50%;
					padding: 0;
					opacity: 1;
					background-color: #f8f8f800 !important;
				}
			}

			.nickname {
				display: flex;
				justify-content: center;
				flex-direction: column;

				.phone {
					color: #ACA8AA;
					font-size: 26rpx;
					margin-top: 15rpx;
				}

				.nickname-box {
					display: flex;
					align-items: center;

					.name-text {
						color: #060A14;
						font-size: 34rpx;
						margin-right: 8rpx;
						font-weight: 700;

						.vip-icon {
							width: 38rpx;
							height: 34rpx;
						}

						.vip-text {
							height: 30rpx;
							font-size: 18rpx;
							color: #A04571;
							background-color: #F6F1F4;
						}
					}
				}

			}

			.info {
				margin-left: 10rpx;
				display: flex;
				justify-content: space-evenly;

				.user-id {
					margin-top: 6rpx;
					font-size: 34rpx;
					color: #060A14;
				}

				.is-vip {
					width: 100rpx;
					height: 38rpx;
					line-height: 38rpx;
					font-size: 22rpx;
					background-color: #999999;
					text-align: center;
					color: #f9f9f9;
					border-radius: 20rpx;
				}
			}

			.login-btn {
				width: 200rpx;

				button {
					height: 100rpx;
					color: 28rpx;
				}
			}

			.vip-code {
				width: 150rpx;
				height: 150rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;

				.code {
					width: 60rpx;
					height: 60rpx;
					margin-bottom: 6rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}
			}

			.login-btn {
				font-size: 26rpx;
				white-space: nowrap;
			}

			.settings {
				image {
					width: 40rpx;
					height: 36rpx;
				}
			}
		}

		.balance-info {
			width: calc(100% - 46rpx);
			height: 136rpx;
			margin-bottom: 28rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: linear-gradient(to right, #EBDDE4 0%, #F7F0F3 100%);
			padding: 0 22rpx;

			.balance-info-box {
				display: flex;
				align-items: center;
			}

			.vip-icon {
				width: 50rpx;
				height: 58rpx;
				margin-right: 12rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			// .vip-code {
			// 	width: 50rpx;
			// 	height: 58rpx;
			// 	margin-right: 12rpx;
			// }

			.vip-info {
				display: flex;
				flex-direction: column;

				.title {
					color: #000000;
					font-size: 28rpx;
					margin-bottom: 16rpx;
				}

				.info {
					color: #ACA8AA;
					font-size: 24rpx;
				}
			}

			.confirm-btn {
				width: 148rpx;
				height: 56rpx;
				border-radius: 28rpx;
				background: linear-gradient(to right, #A04571 0%, #6C2145 100%);
				font-size: 26rpx;
				color: #fff;
				padding: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 0;
			}

			.coupon,
			.balance,
			.integral {
				width: 120rpx;
				height: 120rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-evenly;

				.count {
					font-size: 40rpx;

					.icon {
						font-size: 24rpx;
					}
				}

				.text {
					font-size: 22rpx;
				}
			}
		}
	}

	.option-box {
		width: calc(100% - 46rpx);
		background-color: #fff;
		border-radius: 12rpx;
		padding: 23rpx;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);

		.option-box-item {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			align-items: center;
		}

		.title {
			color: #060A14;
			font-size: 30rpx;
			margin-bottom: 25rpx;
		}

		.option-item {
			width: 25%;
			padding: 10rpx 0;

			.option-content {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;

				image {
					width: 58rpx;
					height: 58rpx;
				}

				text {
					font-size: 24rpx;
				}

				.message-badge {
					position: absolute;
					top: -5rpx;
					right: 15rpx;
					background-color: #ff4757;
					color: white;
					border-radius: 20rpx;
					min-width: 32rpx;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 20rpx;
					font-weight: bold;
					padding: 0 8rpx;
					box-sizing: border-box;
					border: 2rpx solid #fff;
				}
			}
		}
	}

	.order-box {
		width: calc(100% - 46rpx);
		height: 198rpx;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 23rpx;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
		margin-bottom: 28rpx;

		.order-box-item {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			align-items: center;

			.order-content {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;

				image {
					width: 66rpx;
					height: 66rpx;
				}

				text {
					font-size: 24rpx;
				}
			}
		}

		.title {
			color: #060A14;
			font-size: 30rpx;
			margin-bottom: 25rpx;
		}

	}
}

/* 客服弹框样式 */
.service-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.service-modal {
	position: relative;
	width: 520rpx;
	background: #fff;
	border-radius: 32rpx;
	margin: 0 40rpx;
	padding-bottom: 48rpx;
}

.modal-close {
	position: absolute;
	top: -60rpx;
	right: 0;
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 99;

	image {
		width: 32rpx;
		height: 32rpx;
		opacity: 0.6;
	}
}

.service-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.service-icon {
	position: relative;
	width: 100%;
	height: 210rpx;

	image {
		width: 100%;
		height: 100%;
	}
}

.sparkle {
	position: absolute;
	width: 16rpx;
	height: 16rpx;
	background: #D67BA8;
	border-radius: 50%;

	&::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 4rpx;
		height: 4rpx;
		background: #fff;
		border-radius: 50%;
	}
}

.sparkle-1 {
	top: -10rpx;
	left: 20rpx;
	animation: sparkle 2s infinite;
}

.sparkle-2 {
	top: 20rpx;
	right: -10rpx;
	animation: sparkle 2s infinite 0.5s;
}

.sparkle-3 {
	bottom: -10rpx;
	right: 20rpx;
	animation: sparkle 2s infinite 1s;
}

.sparkle-4 {
	bottom: 20rpx;
	left: -10rpx;
	animation: sparkle 2s infinite 1.5s;
}

@keyframes sparkle {

	0%,
	100% {
		opacity: 0;
		transform: scale(0.5);
	}

	50% {
		opacity: 1;
		transform: scale(1);
	}
}

.service-title {
	font-size: 24rpx;
	color: #8B8788;
	margin-bottom: 20rpx;
	font-weight: 500;
}

.service-phone {
	font-size: 40rpx;
	color: #000;
	font-weight: bold;
	margin-bottom: 40rpx;
	letter-spacing: 2rpx;
}

.call-btn {
	width: 370rpx;
	height: 90rpx;
	background: linear-gradient(135deg, #B85A8A 0%, #D67BA8 100%);
	color: #fff;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;

	&:active {
		opacity: 0.8;
	}
}
</style>
