<template>
	<view class="test-page">
		<view class="test-header">
			<text class="test-title">领券中心测试页面</text>
		</view>
		
		<view class="test-buttons">
			<button class="test-btn" @click="goToCouponCenter">
				进入领券中心
			</button>
			
			<button class="test-btn" @click="goToMyPage">
				查看我的页面（优惠券入口）
			</button>
		</view>
		
		<view class="test-info">
			<text class="info-title">功能说明：</text>
			<text class="info-text">1. 点击"进入领券中心"直接跳转到领券中心页面</text>
			<text class="info-text">2. 点击"查看我的页面"可以看到优惠券入口</text>
			<text class="info-text">3. 在我的页面点击优惠券数量可以跳转到领券中心</text>
		</view>
	</view>
</template>

<script setup>
// 跳转到领券中心
const goToCouponCenter = () => {
	uni.navigateTo({
		url: '/pages/coupon/index'
	});
};

// 跳转到我的页面
const goToMyPage = () => {
	uni.switchTab({
		url: '/pages/my/index'
	});
};
</script>

<style lang="scss" scoped>
.test-page {
	padding: 40rpx;
	min-height: 100vh;
	background: #F5F5F5;
	
	.test-header {
		text-align: center;
		margin-bottom: 60rpx;
		
		.test-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
		}
	}
	
	.test-buttons {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
		margin-bottom: 60rpx;
		
		.test-btn {
			padding: 30rpx;
			background: #A04571;
			color: #fff;
			border-radius: 12rpx;
			font-size: 32rpx;
			border: none;
		}
	}
	
	.test-info {
		background: #fff;
		padding: 30rpx;
		border-radius: 12rpx;
		
		.info-title {
			display: block;
			font-size: 28rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.info-text {
			display: block;
			font-size: 26rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 12rpx;
		}
	}
}
</style>
