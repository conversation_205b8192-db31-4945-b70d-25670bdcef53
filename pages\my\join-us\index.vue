<template>
	<view class="container">

		<!-- 内容区域 -->
		<scroll-view class="content" scroll-y>
			<!-- 身份信息 -->
			<view class="section">
				<view class="section-title">身份信息</view>
				<view class="form-item">
					<view class="form-label">真实姓名</view>
					<input class="form-input" v-model="formData.realName" placeholder="请填写" placeholder-class="placeholder" />
				</view>
				<view style="width: 100%; height: 2rpx; background-color: #E7E7E7;"></view>
				<view class="form-item" style="margin-top: 31rpx;">
					<view class="form-label">身份证号</view>
					<input class="form-input" v-model="formData.idCard" placeholder="请填写" placeholder-class="placeholder" />
				</view>
			</view>

			<!-- 证件照片 -->
			<view class="section">
				<view class="section-title">证件照片</view>
				<view class="upload-grid">
					<view class="upload-item" @click="uploadIdCardFront">
						<image v-if="formData.idCardFront" :src="formData.idCardFront" mode="aspectFill"></image>
						<view v-else class="upload-placeholder">
							<view class="upload-img">
								<image src="../../../static/icon/my/组 <EMAIL>"></image>
								<view class="upload-icon">
									<uni-icons type="plusempty" size="20" color="#fff"></uni-icons>
								</view>
							</view>
						</view>
					</view>
					<view class="upload-item" @click="uploadIdCardBack">
						<image v-if="formData.idCardBack" :src="formData.idCardBack" mode="aspectFill"></image>
						<view v-else class="upload-placeholder">
							<view class="upload-img">
								<image src="../../../static/icon/my/组 <EMAIL>"></image>
								<view class="upload-icon">
									<uni-icons type="plusempty" size="20" color="#fff"></uni-icons>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 资格证照片 -->
			<view class="section">
				<view class="section-title">资格证照片</view>
				<view class="upload-single">
					<view class="upload-item" @click="uploadCertificate">
						<image v-if="formData.certificate" :src="formData.certificate" mode="aspectFill"></image>
						<view v-else class="upload-placeholder">
							<view class="upload-img">
								<image src="../../../static/icon/my/组 <EMAIL>"></image>
								<view class="upload-icon">
									<uni-icons type="plusempty" size="20" color="#fff"></uni-icons>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitForm">提交</button>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 表单数据
const formData = reactive({
	realName: '',
	idCard: '',
	idCardFront: '',
	idCardBack: '',
	certificate: ''
})

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 上传身份证正面
const uploadIdCardFront = () => {
	uni.chooseImage({
		count: 1,
		sizeType: ['compressed'],
		sourceType: ['camera', 'album'],
		success: (res) => {
			formData.idCardFront = res.tempFilePaths[0]
		},
		fail: (err) => {
			console.error('选择图片失败:', err)
			uni.showToast({
				title: '选择图片失败',
				icon: 'none'
			})
		}
	})
}

// 上传身份证背面
const uploadIdCardBack = () => {
	uni.chooseImage({
		count: 1,
		sizeType: ['compressed'],
		sourceType: ['camera', 'album'],
		success: (res) => {
			formData.idCardBack = res.tempFilePaths[0]
		},
		fail: (err) => {
			console.error('选择图片失败:', err)
			uni.showToast({
				title: '选择图片失败',
				icon: 'none'
			})
		}
	})
}

// 上传资格证
const uploadCertificate = () => {
	uni.chooseImage({
		count: 1,
		sizeType: ['compressed'],
		sourceType: ['camera', 'album'],
		success: (res) => {
			formData.certificate = res.tempFilePaths[0]
		},
		fail: (err) => {
			console.error('选择图片失败:', err)
			uni.showToast({
				title: '选择图片失败',
				icon: 'none'
			})
		}
	})
}

// 表单验证
const validateForm = () => {
	if (!formData.realName.trim()) {
		uni.showToast({
			title: '请填写真实姓名',
			icon: 'none'
		})
		return false
	}

	if (!formData.idCard.trim()) {
		uni.showToast({
			title: '请填写身份证号',
			icon: 'none'
		})
		return false
	}

	// 身份证号格式验证
	const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
	if (!idCardReg.test(formData.idCard)) {
		uni.showToast({
			title: '身份证号格式不正确',
			icon: 'none'
		})
		return false
	}

	if (!formData.idCardFront) {
		uni.showToast({
			title: '请上传身份证正面照片',
			icon: 'none'
		})
		return false
	}

	if (!formData.idCardBack) {
		uni.showToast({
			title: '请上传身份证背面照片',
			icon: 'none'
		})
		return false
	}

	if (!formData.certificate) {
		uni.showToast({
			title: '请上传资格证照片',
			icon: 'none'
		})
		return false
	}

	return true
}

// 提交表单
const submitForm = () => {
	if (!validateForm()) {
		return
	}

	uni.showLoading({
		title: '提交中...'
	})

	// 模拟提交
	setTimeout(() => {
		uni.hideLoading()
		uni.showToast({
			title: '提交成功',
			icon: 'success'
		})

		// 提交成功后返回上一页
		setTimeout(() => {
			uni.navigateBack()
		}, 1500)
	}, 2000)
}
</script>

<style scoped lang="scss">
.container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.content {
	flex: 1;
	padding: 32rpx;
	width: calc(100% - 64rpx);
}

.section {
	background-color: #fff;
	border-radius: 8rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;

	.section-title {
		font-size: 28rpx;
		font-weight: 500;
		color: #000;
		margin-bottom: 40rpx;
	}
}

.form-item {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}

	.form-label {
		font-size: 28rpx;
		color: #000;
		width: 160rpx;
		flex-shrink: 0;
	}

	.form-input {
		flex: 1;
		font-size: 28rpx;
		color: #8A8788;
		padding: 0;
		border: none;
		outline: none;

		.placeholder {
			color: #8A8788;
		}
	}
}

.upload-grid {
	display: flex;
	gap: 24rpx;
}

.upload-single {
	width: 200rpx;
}

.upload-item {
	width: 302rpx;
	height: 215rpx;
	border-radius: 8rpx;
	overflow: hidden;
	position: relative;

	image {
		width: 100%;
		height: 100%;
	}

	.upload-placeholder {
		width: calc(100% - 80rpx);
		height: calc(100% - 80rpx);
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #F5F5F5;
		padding: 40rpx;
		position: relative;

		.upload-img {
			width: calc(100% - 48rpx);
			height: calc(100% - 48rpx);
			background-color: #fff;
			padding: 24rpx;
			border-radius: 8rpx;
		}

		.upload-icon {
			width: 48rpx;
			height: 48rpx;
			border-radius: 50%;
			background-color: #ad3d72;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}
}

.submit-section {
	padding: 32rpx 0;
}

.submit-btn {
	width: 100%;
	height: 98rpx;
	background: #A04571;
	border-radius: 18rpx;
	border: none;
	color: #fff;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;

	&:active {
		opacity: 0.8;
	}
}
</style>
