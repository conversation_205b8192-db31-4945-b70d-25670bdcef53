<template>
	<view class="payment-page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<view class="nav-title">支付</view>
			<view class="nav-right"></view>
		</view>

		<!-- 订单信息 -->
		<view class="order-info">
			<view class="order-title">{{ planName }}会员</view>
			<view class="order-price">¥{{ price }}</view>
		</view>

		<!-- 支付方式 -->
		<view class="payment-methods">
			<view class="section-title">选择支付方式</view>
			<view class="method-list">
				<view class="method-item" :class="{ 'selected': selectedMethod === 'wechat' }" @click="selectMethod('wechat')">
					<view class="method-info">
						<image class="method-icon" src="/static/icon/wechat-pay.png" mode="aspectFit"></image>
						<text class="method-name">微信支付</text>
					</view>
					<view class="method-radio">
						<view class="radio" :class="{ 'checked': selectedMethod === 'wechat' }"></view>
					</view>
				</view>

				<view class="method-item" :class="{ 'selected': selectedMethod === 'alipay' }" @click="selectMethod('alipay')">
					<view class="method-info">
						<image class="method-icon" src="/static/icon/alipay.png" mode="aspectFit"></image>
						<text class="method-name">支付宝</text>
					</view>
					<view class="method-radio">
						<view class="radio" :class="{ 'checked': selectedMethod === 'alipay' }"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部支付按钮 -->
		<view class="bottom-payment">
			<view class="payment-info">
				<text class="total-text">合计：</text>
				<text class="total-price">¥{{ price }}</text>
			</view>
			<button class="pay-btn" @click="handlePay">立即支付</button>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from "@dcloudio/uni-app";

// 页面参数
const plan = ref('');
const price = ref('');
const planName = ref('');

// 选中的支付方式
const selectedMethod = ref('wechat');

// 选择支付方式
const selectMethod = (method) => {
	selectedMethod.value = method;
};

// 返回上一页
const goBack = () => {
	uni.navigateBack();
};

// 处理支付
const handlePay = () => {
	uni.showLoading({
		title: '支付中...'
	});

	// 模拟支付过程
	setTimeout(() => {
		uni.hideLoading();
		uni.showModal({
			title: '支付成功',
			content: `恭喜您成功开通${planName.value}会员！`,
			showCancel: false,
			success: () => {
				// 跳转到支付成功页面或返回会员中心
				uni.navigateTo({
					url: '/pages/payment/success/index'
				});
			}
		});
	}, 2000);
};

// 页面加载
onLoad((options) => {
	if (options.plan) {
		plan.value = options.plan;
	}
	if (options.price) {
		price.value = options.price;
	}
	if (options.planName) {
		planName.value = options.planName;
	}
});
</script>

<style lang="scss" scoped>
.payment-page {
	min-height: 100vh;
	background: #F5F5F5;

	// 顶部导航栏
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 32rpx;
		padding-top: calc(var(--status-bar-height) + 20rpx);
		background: #fff;

		.nav-left {
			width: 60rpx;
		}

		.nav-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
		}

		.nav-right {
			width: 60rpx;
		}
	}

	// 订单信息
	.order-info {
		padding: 40rpx 32rpx;
		background: #fff;
		margin-bottom: 20rpx;
		text-align: center;

		.order-title {
			font-size: 32rpx;
			color: #333;
			margin-bottom: 16rpx;
		}

		.order-price {
			font-size: 48rpx;
			color: #D4749A;
			font-weight: 700;
		}
	}

	// 支付方式
	.payment-methods {
		background: #fff;
		padding: 32rpx;

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 24rpx;
		}

		.method-list {
			.method-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 24rpx 0;
				border-bottom: 1rpx solid #F0F0F0;

				&:last-child {
					border-bottom: none;
				}

				&.selected {
					.method-name {
						color: #D4749A;
					}
				}

				.method-info {
					display: flex;
					align-items: center;

					.method-icon {
						width: 48rpx;
						height: 48rpx;
						margin-right: 16rpx;
					}

					.method-name {
						font-size: 30rpx;
						color: #333;
					}
				}

				.method-radio {
					.radio {
						width: 32rpx;
						height: 32rpx;
						border: 2rpx solid #E0E0E0;
						border-radius: 50%;
						position: relative;

						&.checked {
							border-color: #D4749A;

							&::after {
								content: '';
								position: absolute;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);
								width: 16rpx;
								height: 16rpx;
								background: #D4749A;
								border-radius: 50%;
							}
						}
					}
				}
			}
		}
	}

	// 底部支付按钮
	.bottom-payment {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		align-items: center;
		padding: 24rpx 32rpx;
		background: #fff;
		border-top: 1rpx solid #E0E0E0;

		.payment-info {
			flex: 1;

			.total-text {
				font-size: 28rpx;
				color: #666;
			}

			.total-price {
				font-size: 36rpx;
				color: #D4749A;
				font-weight: 700;
			}
		}

		.pay-btn {
			padding: 24rpx 48rpx;
			background: #D4749A;
			color: #fff;
			border-radius: 12rpx;
			font-size: 32rpx;
			font-weight: 600;
			border: none;
		}
	}
}
</style>
