<template>
	<view class="contact-container">
		<view class="header">
			<view class="avatar">
				<!-- <image src="../../../static/icon/my/客服.png" mode="aspectFit"></image> -->
			</view>
			<view class="info">
				<text class="name">熙桓心理客服</text>
				<text class="status">在线</text>
			</view>
		</view>

		<view class="contact-methods">
			<view class="method-item" @click="callPhone">
				<view class="method-icon">
					<!-- <image src="../../../static/icon/my/个人.png" mode="aspectFit"></image> -->
				</view>
				<view class="method-info">
					<text class="method-title">电话咨询</text>
					<text class="method-desc">400-xxx-xxxx</text>
				</view>
				<view class="method-arrow">
					<image src="../../../static/icon/my/形状 8.png" mode="aspectFit"></image>
				</view>
			</view>

			<view class="method-item" @click="openWechat">
				<view class="method-icon">
					<!-- <image src="../../../static/icon/my/个人.png" mode="aspectFit"></image> -->
				</view>
				<view class="method-info">
					<text class="method-title">微信客服</text>
					<text class="method-desc">添加微信好友</text>
				</view>
				<view class="method-arrow">
					<image src="../../../static/icon/my/形状 8.png" mode="aspectFit"></image>
				</view>
			</view>

			<view class="method-item" @click="sendEmail">
				<view class="method-icon">
					<!-- <image src="../../../static/icon/my/个人.png" mode="aspectFit"></image> -->
				</view>
				<view class="method-info">
					<text class="method-title">邮箱反馈</text>
					<text class="method-desc"><EMAIL></text>
				</view>
				<view class="method-arrow">
					<image src="../../../static/icon/my/形状 8.png" mode="aspectFit"></image>
				</view>
			</view>
		</view>

		<view class="service-time">
			<view class="time-title">服务时间</view>
			<view class="time-content">
				<text>周一至周日 9:00-21:00</text>
			</view>
		</view>

		<view class="faq-section">
			<view class="faq-title">常见问题</view>
			<view class="faq-list">
				<view class="faq-item" v-for="(item, index) in faqList" :key="index" @click="showFaqAnswer(item)">
					<text class="faq-question">{{ item.question }}</text>
					<image class="faq-arrow" src="../../../static/icon/my/形状 8.png" mode="aspectFit"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'

// 常见问题列表
const faqList = ref([
	{
		question: '如何预约心理咨询？',
		answer: '您可以在首页选择咨询师，点击预约按钮，选择合适的时间进行预约。'
	},
	{
		question: '咨询费用如何计算？',
		answer: '咨询费用根据咨询师的级别和咨询时长计算，具体费用在预约时会显示。'
	},
	{
		question: '如何取消或修改预约？',
		answer: '您可以在"我的预约"中查看并管理您的预约，提前24小时可免费取消或修改。'
	},
	{
		question: '咨询记录会保密吗？',
		answer: '我们严格遵守保密原则，您的咨询内容和个人信息都会得到严格保护。'
	}
])

// 拨打电话
const callPhone = () => {
	uni.makePhoneCall({
		phoneNumber: '400-xxx-xxxx'
	})
}

// 打开微信
const openWechat = () => {
	uni.showModal({
		title: '微信客服',
		content: '请添加微信号：xihuanxinli_service',
		showCancel: false
	})
}

// 发送邮件
const sendEmail = () => {
	uni.setClipboardData({
		data: '<EMAIL>',
		success: () => {
			uni.showToast({
				title: '邮箱地址已复制',
				icon: 'success'
			})
		}
	})
}

// 显示常见问题答案
const showFaqAnswer = (item) => {
	uni.showModal({
		title: item.question,
		content: item.answer,
		showCancel: false
	})
}
</script>

<style scoped lang="scss">
.contact-container {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding: 32rpx;
}

.header {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 32rpx;
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	margin-right: 24rpx;

	image {
		width: 100%;
		height: 100%;
		border-radius: 60rpx;
	}
}

.info {
	flex: 1;
}

.name {
	display: block;
	font-size: 36rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.status {
	font-size: 28rpx;
	color: #52c41a;
}

.contact-methods {
	background-color: #fff;
	border-radius: 12rpx;
	margin-bottom: 32rpx;
	overflow: hidden;
}

.method-item {
	display: flex;
	align-items: center;
	padding: 32rpx;
	border-bottom: 1rpx solid #f5f5f5;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f5f5f5;
	}
}

.method-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 24rpx;

	image {
		width: 100%;
		height: 100%;
	}
}

.method-info {
	flex: 1;
}

.method-title {
	display: block;
	font-size: 32rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.method-desc {
	font-size: 28rpx;
	color: #666;
}

.method-arrow {
	width: 24rpx;
	height: 24rpx;

	image {
		width: 100%;
		height: 100%;
	}
}

.service-time {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
}

.time-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 16rpx;
}

.time-content {
	font-size: 28rpx;
	color: #666;
}

.faq-section {
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
}

.faq-title {
	padding: 32rpx 32rpx 16rpx;
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.faq-list {
	padding: 0 32rpx 16rpx;
}

.faq-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f5f5f5;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f5f5f5;
	}
}

.faq-question {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.faq-arrow {
	width: 24rpx;
	height: 24rpx;
}
</style>
