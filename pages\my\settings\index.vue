<template>
	<view class="settings-container">
		<!-- 个人信息设置 -->
		<view class="settings-section">
			<view class="settings-item" @click="goToPersonalInfo">
				<text class="item-title">个人资料</text>
				<view class="item-extra">
					<image src="../../../static/icon/圆角矩形 1.png" mode="aspectFit"></image>
				</view>
			</view>
			<view class="settings-item" @click="goToUserAgreement">
				<text class="item-title">用户协议</text>
				<view class="item-extra">
					<image src="../../../static/icon/圆角矩形 1.png" mode="aspectFit"></image>
				</view>
			</view>

			<view class="settings-item" @click="goToPrivacyPolicy">
				<text class="item-title">隐私协议</text>
				<view class="item-extra">
					<image src="../../../static/icon/圆角矩形 1.png" mode="aspectFit"></image>
				</view>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout-section" v-if="userStore.token">
			<button class="logout-btn" @click="showLogoutConfirm">退出登录</button>
		</view>

		<!-- 退出确认弹窗 -->
		<uni-popup ref="logoutPopup" type="dialog" v-if="showLogoutModal">
			<uni-popup-dialog type="confirm" title="确认退出" content="确定要退出登录吗？" @confirm="confirmLogout"
				@close="closeLogoutModal"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { removeToken } from '@/utils/auth.js'

const userStore = useUserStore()

// 响应式数据
const showLogoutModal = ref(false)
const logoutPopup = ref(null)

// 生命周期
onMounted(() => {
	// 初始化设置
})

// 跳转到个人资料
const goToPersonalInfo = () => {
	uni.navigateTo({
		url: '/pages/my/profile/index'
	})
}

// 跳转到用户协议
const goToUserAgreement = () => {
	uni.navigateTo({
		url: '/pages/my/user-agreement/index'
	})
}

// 跳转到隐私政策
const goToPrivacyPolicy = () => {
	uni.navigateTo({
		url: '/pages/my/privacy-policy/index'
	})
}

// 显示退出确认弹窗
const showLogoutConfirm = () => {
	showLogoutModal.value = true
	logoutPopup.value.open()
}

// 关闭退出确认弹窗
const closeLogoutModal = () => {
	showLogoutModal.value = false
	logoutPopup.value.close()
}

// 确认退出登录
const confirmLogout = () => {
	// 清除用户信息和token
	userStore.clearUser()

	uni.showToast({
		title: '已退出登录',
		icon: 'success'
	})

	// 关闭弹窗
	closeLogoutModal()

	// 跳转到登录页面
	setTimeout(() => {
		uni.reLaunch({
			url: '/pages/login/login'
		})
	}, 1500)
}
</script>

<style scoped lang="scss">
.settings-container {
	background-color: #f5f5f5;
	min-height: calc(100vh - 24rpx);
	padding-top: 24rpx;
}

.settings-section {
	background-color: #fff;
	border-radius: 12rpx;
	margin-bottom: 32rpx;
	overflow: hidden;
}


.settings-item {
	display: flex;
	align-items: center;
	padding: 30rpx 32rpx;
	border-bottom: 1rpx solid #f5f5f5;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f5f5f5;
	}
}

.item-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 24rpx;

	image {
		width: 100%;
		height: 100%;
	}
}

.item-title {
	flex: 1;
	font-size: 28rpx;
	color: #000;
}

.item-extra {
	display: flex;
	align-items: center;

	image {
		width: 24rpx;
		height: 24rpx;
	}
}

.logout-section {
	width: calc(100% - 64rpx);
	position: fixed;
	bottom: 98rpx;
	padding: 0 32rpx;
}

.logout-btn {
	width: 100%;
	height: 98rpx;
	background-color: #ad3d72;
	border-radius: 18rpx;
	font-size: 32rpx;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 32rpx;

	&::after {
		border: none;
	}
}
</style>
