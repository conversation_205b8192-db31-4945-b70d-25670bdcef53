<template>
  <view class="test-container">
    <uni-nav-bar title="Echarts滚动测试" left-icon="back" @clickLeft="goBack" />
    
    <scroll-view scroll-y class="scroll-container">
      <!-- 顶部内容 -->
      <view class="content-section">
        <text class="section-title">顶部内容区域</text>
        <view class="content-item" v-for="i in 3" :key="i">
          <text>这是第 {{ i }} 个内容项</text>
        </view>
      </view>

      <!-- echarts测试区域 -->
      <view class="chart-test-section">
        <text class="section-title">Echarts图表测试</text>
        
        <!-- 原始echarts方式（可能有层级问题） -->
        <view class="test-item">
          <text class="test-label">原始Echarts（可能有层级问题）:</text>
          <view class="chart-container">
            <l-echart 
              ref="originalChart"
              class="original-chart"
              :is-disable-scroll="true"
              type="2d"
              :custom-style="'width: 300rpx; height: 300rpx;'"
            >
            </l-echart>
          </view>
        </view>

        <!-- 修复后的方式（转换为图片） -->
        <view class="test-item">
          <text class="test-label">修复后方式（转换为图片）:</text>
          <view class="chart-container">
            <!-- 隐藏的echarts -->
            <l-echart 
              v-show="!fixedChartImage"
              ref="fixedChart"
              class="fixed-chart"
              :is-disable-scroll="true"
              type="2d"
              :custom-style="'width: 300rpx; height: 300rpx;'"
            >
            </l-echart>
            
            <!-- 显示的图片 -->
            <image 
              v-show="fixedChartImage"
              :src="fixedChartImage" 
              mode="aspectFit"
              class="chart-image"
            />
            
            <!-- 加载状态 -->
            <view v-show="isGenerating" class="loading">
              <uni-icons type="spinner-cycle" size="40" color="#A04571"></uni-icons>
              <text class="loading-text">生成中...</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 中间内容 -->
      <view class="content-section">
        <text class="section-title">中间内容区域</text>
        <view class="content-item" v-for="i in 8" :key="`mid-${i}`">
          <text>中间内容项 {{ i }}</text>
        </view>
      </view>

      <!-- 底部内容 -->
      <view class="content-section">
        <text class="section-title">底部内容区域</text>
        <view class="content-item" v-for="i in 3" :key="`bottom-${i}`">
          <text>底部内容项 {{ i }}</text>
        </view>
      </view>

      <!-- 固定在底部的元素（用于测试层级） -->
      <view class="fixed-bottom">
        <text>固定底部元素 - 测试层级</text>
      </view>
    </scroll-view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button @click="generateOriginalChart" class="btn">生成原始图表</button>
      <button @click="generateFixedChart" class="btn">生成修复图表</button>
      <button @click="refreshTest" class="btn">刷新测试</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'

// 小程序使用 require 导入 echarts
// #ifdef MP
const echarts = require('../uni_modules/lime-echart/static/echarts.min');
// #endif
// #ifndef MP
import * as echarts from 'echarts/core';
import { RadarChart } from 'echarts/charts';
import { TitleComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([RadarChart, TitleComponent, LegendComponent, CanvasRenderer]);
// #endif

// 状态管理
const fixedChartImage = ref('')
const isGenerating = ref(false)
const originalChart = ref(null)
const fixedChart = ref(null)

// 图表配置
const getChartOption = (title) => ({
  title: {
    text: title,
    left: 'center',
    textStyle: {
      fontSize: 14,
      color: '#333'
    }
  },
  radar: {
    indicator: [
      { name: '基本信息', max: 100 },
      { name: '性格风格', max: 100 },
      { name: '咨询结构', max: 100 },
      { name: '咨询方式', max: 100 },
      { name: '理论取向', max: 100 }
    ],
    radius: '60%',
    splitNumber: 4,
    axisLine: {
      lineStyle: {
        color: '#E8E8E8',
        width: 1
      }
    },
    splitLine: {
      lineStyle: {
        color: '#E8E8E8',
        width: 1
      }
    },
    splitArea: {
      show: true,
      areaStyle: {
        color: ['rgba(250, 250, 250, 0.3)', 'rgba(245, 245, 245, 0.3)']
      }
    },
    name: {
      textStyle: {
        color: '#666',
        fontSize: 10
      }
    }
  },
  series: [{
    type: 'radar',
    data: [{
      value: [98, 100, 100, 100, 96],
      name: '匹配度',
      areaStyle: {
        color: 'rgba(160, 69, 113, 0.2)'
      },
      lineStyle: {
        color: '#A04571',
        width: 2
      },
      itemStyle: {
        color: '#A04571',
        borderColor: '#A04571',
        borderWidth: 2
      }
    }]
  }]
})

// 生成原始图表
const generateOriginalChart = async () => {
  try {
    await nextTick()
    const chart = await originalChart.value.init(echarts)
    chart.setOption(getChartOption('原始图表'))
  } catch (error) {
    console.error('原始图表生成失败:', error)
  }
}

// 生成修复后的图表
const generateFixedChart = async () => {
  if (isGenerating.value) return
  
  isGenerating.value = true
  fixedChartImage.value = ''
  
  try {
    await nextTick()
    const chart = await fixedChart.value.init(echarts)
    chart.setOption(getChartOption('修复图表'))
    
    // 延迟转换为图片
    setTimeout(async () => {
      try {
        await fixedChart.value.canvasToTempFilePath({
          success: (res) => {
            fixedChartImage.value = res.tempFilePath
            isGenerating.value = false
            console.log('图表转换为图片成功')
          },
          fail: (err) => {
            console.error('图表转换为图片失败:', err)
            isGenerating.value = false
          }
        })
      } catch (error) {
        console.error('转换失败:', error)
        isGenerating.value = false
      }
    }, 1000)
    
  } catch (error) {
    console.error('修复图表生成失败:', error)
    isGenerating.value = false
  }
}

// 刷新测试
const refreshTest = () => {
  fixedChartImage.value = ''
  
  setTimeout(() => {
    generateOriginalChart()
    generateFixedChart()
  }, 100)
}

// 返回
const goBack = () => {
  uni.navigateBack()
}

// 页面加载时生成图表
onMounted(() => {
  setTimeout(() => {
    generateOriginalChart()
    generateFixedChart()
  }, 500)
})
</script>

<style lang="scss" scoped>
.test-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  background: #f5f5f6;
}

.content-section {
  background: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }

  .content-item {
    padding: 20rpx 0;
    border-bottom: 1rpx solid #eee;

    &:last-child {
      border-bottom: none;
    }
  }
}

.chart-test-section {
  background: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
    display: block;
  }

  .test-item {
    margin-bottom: 40rpx;
    
    .test-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 20rpx;
      display: block;
    }

    .chart-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300rpx;
      position: relative;
      border: 1rpx solid #eee;
      border-radius: 8rpx;

      .original-chart {
        width: 300rpx;
        height: 300rpx;
      }

      .fixed-chart {
        width: 300rpx;
        height: 300rpx;
        position: absolute;
        top: -1000rpx;
        left: -1000rpx;
      }

      .chart-image {
        width: 300rpx;
        height: 300rpx;
      }

      .loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .loading-text {
          font-size: 24rpx;
          color: #A04571;
          margin-top: 16rpx;
        }
      }
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  z-index: 999;
}

.action-buttons {
  display: flex;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #eee;

  .btn {
    flex: 1;
    margin: 0 10rpx;
    background: #A04571;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}
</style>
