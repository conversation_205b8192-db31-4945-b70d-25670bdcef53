<template>
  <view class="demo-page">
    <view class="demo-header">
      <text class="demo-title">订单详情组件演示</text>
      <text class="demo-desc">展示不同订单类型和状态的效果</text>
    </view>

    <view class="demo-controls">
      <view class="control-group">
        <text class="control-label">订单类型：</text>
        <picker 
          :value="selectedTypeIndex" 
          :range="orderTypeOptions" 
          range-key="label"
          @change="onTypeChange"
        >
          <view class="picker-value">{{ orderTypeOptions[selectedTypeIndex].label }}</view>
        </picker>
      </view>

      <view class="control-group">
        <text class="control-label">订单状态：</text>
        <picker 
          :value="selectedStatusIndex" 
          :range="orderStatusOptions" 
          range-key="label"
          @change="onStatusChange"
        >
          <view class="picker-value">{{ orderStatusOptions[selectedStatusIndex].label }}</view>
        </picker>
      </view>
    </view>

    <UniversalOrderDetail 
      :orderData="demoOrderData"
      @action="handleAction"
      @share="handleShare"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ORDER_STATUS, ORDER_TYPES } from '@/api/payment.js'
import UniversalOrderDetail from '@/components/UniversalOrderDetail/UniversalOrderDetail.vue'

// 订单类型选项
const orderTypeOptions = [
  { label: '心理咨询', value: ORDER_TYPES.CONSULTANT },
  { label: '心理课程', value: ORDER_TYPES.COURSE },
  { label: '冥想内容', value: ORDER_TYPES.MEDITATION },
  { label: '心理测评', value: ORDER_TYPES.ASSESSMENT }
]

// 订单状态选项
const orderStatusOptions = [
  { label: '待支付', value: ORDER_STATUS.PENDING },
  { label: '已支付', value: ORDER_STATUS.PAID },
  { label: '进行中', value: ORDER_STATUS.PROCESSING },
  { label: '已完成', value: ORDER_STATUS.COMPLETED },
  { label: '已取消', value: ORDER_STATUS.CANCELLED },
  { label: '退款中', value: ORDER_STATUS.REFUNDING },
  { label: '已退款', value: ORDER_STATUS.REFUNDED }
]

// 当前选择
const selectedTypeIndex = ref(0)
const selectedStatusIndex = ref(0)

// 演示订单数据
const demoOrderData = computed(() => {
  const currentType = orderTypeOptions[selectedTypeIndex.value].value
  const currentStatus = orderStatusOptions[selectedStatusIndex.value].value

  const baseData = {
    orderNo: 'XH' + Date.now(),
    orderType: currentType,
    status: currentStatus,
    userPhone: '13812345678',
    createTime: new Date().toISOString(),
    payTime: currentStatus !== ORDER_STATUS.PENDING ? new Date().toISOString() : null,
    price: 299,
    paymentAmount: 299
  }

  // 根据订单类型设置不同的商品信息
  switch (currentType) {
    case ORDER_TYPES.CONSULTANT:
      return {
        ...baseData,
        productName: '资深心理咨询师 - 个人咨询服务',
        productImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
        consultantLevel: '资深咨询师',
        appointmentTime: currentStatus !== ORDER_STATUS.PENDING ? new Date().toISOString() : null,
        price: 399,
        paymentAmount: 399
      }

    case ORDER_TYPES.COURSE:
      return {
        ...baseData,
        productName: '情绪管理与压力缓解课程',
        productImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
        price: 199,
        paymentAmount: 199
      }

    case ORDER_TYPES.MEDITATION:
      return {
        ...baseData,
        productName: '正念冥想引导音频包',
        productImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
        price: 99,
        paymentAmount: 99
      }

    case ORDER_TYPES.ASSESSMENT:
      return {
        ...baseData,
        productName: '专业心理健康评估测试',
        productImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png',
        price: 59,
        paymentAmount: 59
      }

    default:
      return baseData
  }
})

// 处理类型变更
const onTypeChange = (e) => {
  selectedTypeIndex.value = e.detail.value
}

// 处理状态变更
const onStatusChange = (e) => {
  selectedStatusIndex.value = e.detail.value
}

// 处理操作
const handleAction = ({ type, orderData }) => {
  uni.showToast({
    title: `演示模式 - ${type}`,
    icon: 'none'
  })
  console.log('操作类型:', type, '订单数据:', orderData)
}

// 处理分享
const handleShare = (orderData) => {
  uni.showToast({
    title: '演示模式 - 分享',
    icon: 'none'
  })
  console.log('分享订单:', orderData)
}
</script>

<style lang="scss" scoped>
.demo-page {
  min-height: 100vh;
  background-color: #f5f5f6;
}

.demo-header {
  background: #fff;
  padding: 40rpx 32rpx;
  margin-bottom: 20rpx;
  text-align: center;

  .demo-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .demo-desc {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.4;
  }
}

.demo-controls {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 20rpx;

  .control-group {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .control-label {
      width: 120rpx;
      font-size: 28rpx;
      color: #333;
      flex-shrink: 0;
    }

    .picker-value {
      flex: 1;
      padding: 16rpx 24rpx;
      background: #f5f5f6;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #333;
      text-align: center;
    }
  }
}
</style>
