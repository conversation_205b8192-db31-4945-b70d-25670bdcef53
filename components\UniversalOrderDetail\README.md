# UniversalOrderDetail 通用订单详情组件

## 概述

UniversalOrderDetail 是一个高度可扩展的订单详情组件，支持多种订单类型（咨询、课程、冥想、测评）和多种订单状态。该组件采用组件化设计，可以在不同页面中复用，提供统一的订单详情展示界面。

## 特性

- ✅ 支持多种订单类型：心理咨询、心理课程、冥想内容、心理测评
- ✅ 支持多种订单状态：待支付、已支付、进行中、已完成、已取消、退款中、已退款
- ✅ 自适应状态显示：根据订单状态自动显示相应的UI元素
- ✅ 可自定义操作按钮：支持自定义底部操作按钮
- ✅ 可自定义使用须知：支持自定义使用规则
- ✅ 响应式设计：适配不同屏幕尺寸
- ✅ 事件驱动：通过事件与父组件通信

## 使用方法

### 基础用法

```vue
<template>
  <UniversalOrderDetail 
    :orderData="orderInfo"
    @action="handleAction"
    @share="handleShare"
  />
</template>

<script setup>
import UniversalOrderDetail from '@/components/UniversalOrderDetail/UniversalOrderDetail.vue'

const orderInfo = ref({
  orderNo: 'XH123456789',
  orderType: 'consultant',
  status: 'paid',
  productName: '资深心理咨询师 - 个人咨询服务',
  productImage: 'https://example.com/image.jpg',
  price: 399,
  paymentAmount: 399,
  userPhone: '13812345678',
  createTime: '2024-01-01T10:00:00Z',
  payTime: '2024-01-01T10:05:00Z'
})

const handleAction = ({ type, orderData }) => {
  // 处理操作事件
  console.log('操作类型:', type, '订单数据:', orderData)
}

const handleShare = (orderData) => {
  // 处理分享事件
  console.log('分享订单:', orderData)
}
</script>
```

### 高级用法

```vue
<template>
  <UniversalOrderDetail 
    :orderData="orderInfo"
    :shopIcon="customShopIcon"
    :shopName="customShopName"
    :qrCodeDesc="customQRDesc"
    :customRules="customUsageRules"
    :customActions="customActionButtons"
    @action="handleAction"
    @share="handleShare"
  />
</template>

<script setup>
const customUsageRules = [
  { label: '有效期', value: '购买后90天内有效' },
  { label: '使用说明', value: '请提前预约使用时间' },
  { label: '退款政策', value: '7天内可申请退款' }
]

const customActionButtons = [
  { type: 'contact-service', text: '联系客服', class: 'contact-btn' },
  { type: 'view-details', text: '查看详情', class: 'detail-btn' },
  { type: 'download', text: '下载凭证', class: 'download-btn' }
]
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| orderData | Object | {} | 订单数据对象 |
| shopIcon | String | '../../static/icon/logo.png' | 商店图标 |
| shopName | String | '熙桓心理咨询阳店' | 商店名称 |
| qrCodeDesc | String | '为了保证您的权益，请勿将此码泄露给他人' | 二维码描述 |
| customRules | Array | [] | 自定义使用须知 |
| customActions | Array | [] | 自定义操作按钮 |

### orderData 对象结构

```javascript
{
  orderNo: String,           // 订单号
  orderType: String,         // 订单类型：consultant/course/meditation/assessment
  status: String,            // 订单状态：pending/paid/processing/completed/cancelled/refunding/refunded
  productName: String,       // 商品名称
  productImage: String,      // 商品图片
  price: Number,             // 原价
  paymentAmount: Number,     // 实付金额
  userPhone: String,         // 用户手机号
  createTime: String,        // 创建时间（ISO格式）
  payTime: String,           // 支付时间（ISO格式）
  expireTime: String,        // 过期时间（ISO格式）
  consultantLevel: String,   // 咨询师等级（仅咨询类型）
  appointmentTime: String    // 预约时间（仅咨询类型）
}
```

### customRules 数组结构

```javascript
[
  {
    label: String,  // 规则标签
    value: String   // 规则内容
  }
]
```

### customActions 数组结构

```javascript
[
  {
    type: String,   // 操作类型
    text: String,   // 按钮文本
    class: String   // 按钮样式类
  }
]
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| action | { type: String, orderData: Object } | 操作按钮点击事件 |
| share | orderData: Object | 分享按钮点击事件 |

### 默认操作类型

- `contact-service`: 联系客服
- `cancel-order`: 取消订单
- `pay-order`: 立即支付
- `refund`: 申请退款

## 订单状态说明

| 状态值 | 状态名称 | 显示元素 |
|--------|----------|----------|
| pending | 待支付 | 取消订单、立即支付按钮 |
| paid | 已支付 | 二维码、使用须知、退款按钮 |
| processing | 进行中 | 二维码、使用须知、退款按钮 |
| completed | 已完成 | 二维码、使用须知 |
| cancelled | 已取消 | 无特殊操作 |
| refunding | 退款中 | 无特殊操作 |
| refunded | 已退款 | 无特殊操作 |

## 订单类型说明

| 类型值 | 类型名称 | 特殊字段 |
|--------|----------|----------|
| consultant | 心理咨询 | consultantLevel, appointmentTime |
| course | 心理课程 | 无 |
| meditation | 冥想内容 | 无 |
| assessment | 心理测评 | 无 |

## 样式自定义

组件使用 SCSS 编写样式，支持通过 CSS 变量或覆盖样式类进行自定义。主要样式类包括：

- `.status-card`: 状态卡片
- `.product-card`: 商品信息卡片
- `.qr-card`: 二维码卡片
- `.rules-card`: 使用须知卡片
- `.order-info-card`: 订单信息卡片
- `.bottom-actions`: 底部操作按钮

## 注意事项

1. 确保传入的 `orderData` 包含必要的字段
2. 时间字段应使用 ISO 格式字符串
3. 图片链接应确保可访问性
4. 自定义操作按钮需要在父组件中处理相应的事件
5. 组件依赖 `@/api/payment.js` 中的常量定义

## 演示页面

可以访问 `/pages/order/demo/index` 查看组件的演示效果，该页面展示了不同订单类型和状态的显示效果。
