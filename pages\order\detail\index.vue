<template>
  <view class="order-detail">
    <UniversalOrderDetail
      :orderData="orderInfo"
      @action="handleAction"
      @share="handleShare"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getOrderDetail } from '@/api/payment.js'
import { requestWxPayment } from '@/utils/payment.js'
import UniversalOrderDetail from '@/components/UniversalOrderDetail/UniversalOrderDetail.vue'

// 响应式数据
const orderInfo = ref({})
const orderNo = ref('')
const loading = ref(false)

// 生命周期
onLoad((options) => {
  orderNo.value = options.orderNo
  if (orderNo.value) {
    loadOrderDetail()
  }
})

// 加载订单详情
const loadOrderDetail = async () => {
  try {
    loading.value = true
    const res = await getOrderDetail(orderNo.value)

    if (res.code === 200) {
      orderInfo.value = res.data
    } else {
      throw new Error(res.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    uni.showToast({
      title: error.message || '获取订单详情失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 处理操作
const handleAction = async ({ type, orderData }) => {
  switch (type) {
    case 'contact-service':
      uni.navigateTo({
        url: '/pages/my/my-message/chat/chat?conversationId=137&userId=&consultantId=137&nickname=熙桓心理客服'
      })
      break

    case 'cancel-order':
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个订单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '取消订单功能开发中',
              icon: 'none'
            })
          }
        }
      })
      break

    case 'pay-order':
      try {
        const payResult = await requestWxPayment(orderNo.value)
        if (payResult.success) {
          uni.showToast({
            title: '支付成功',
            icon: 'success'
          })
          setTimeout(() => {
            loadOrderDetail()
          }, 1000)
        }
      } catch (error) {
        if (!error.canceled) {
          uni.showToast({
            title: error.message || '支付失败',
            icon: 'none'
          })
        }
      }
      break

    case 'refund':
      uni.showModal({
        title: '申请退款',
        content: '确定要申请退款吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '退款功能开发中',
              icon: 'none'
            })
          }
        }
      })
      break

    default:
      console.log('未知操作类型:', type)
  }
}

// 处理分享
const handleShare = (orderData) => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.order-detail {
  min-height: 100vh;
  background-color: #f5f5f6;
}
</style>
