<template>
	<view class="content">
		<!-- 分类标签页 -->
		<!-- <view class="category-tabs">
			<view v-for="(tab, index) in tabs" :key="tab.type" :class="['tab-item', { active: currentTab === index }]"
				@click="switchTab(index)">
				{{ tab.name }}
			</view>
		</view> -->
		<view class="result-category-tabs">
			<view v-for="(tab, index) in tabs" :key="tab.type" :class="['tab-item', { active: currentTab === index }]"
				@click="switchTab(index)">
				{{ tab.name }}
				<image v-if="currentTab === index" class="tab-icon" src="../../../static/icon/形状 6-active.png"></image>
			</view>
		</view>

		<!-- 咨询师收藏列表 -->
		<view v-if="currentTab === 0" class="list-content">
			<UniversalListItem v-for="item in counselorList" :key="item.counselorId" :item="item" type="consultant"
				:dictData="{ psy_consultant_level }" @click="handleCounselorDetail" />
			<view v-if="counselorList.length === 0" class="empty-tip">暂无收藏的咨询师</view>
		</view>

		<!-- 课程收藏列表 -->
		<view v-if="currentTab === 1" class="list-content">
			<UniversalListItem v-for="item in courseList" :key="item.productId" :item="item" type="course"
				@click="handleCourseDetail" />
			<view v-if="courseList.length === 0" class="empty-tip">暂无收藏的课程</view>
		</view>

		<!-- 冥想收藏列表 -->
		<view v-if="currentTab === 2" class="list-content">
			<UniversalListItem v-for="item in meditationList" :key="item.meditationId" :item="item" type="meditation"
				@click="handleMeditationDetail" />
			<view v-if="meditationList.length === 0" class="empty-tip">暂无收藏的冥想</view>
		</view>

		<!-- 测评收藏列表 -->
		<view v-if="currentTab === 3" class="list-content">
			<UniversalListItem v-for="item in assessmentList" :key="item.assessmentId" :item="item" type="assessment"
				@click="handleAssessmentDetail" />
			<view v-if="assessmentList.length === 0" class="empty-tip">暂无收藏的测评</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getFavoriteList, FAVORITE_TYPES } from "@/api/favorite.js";
import { useDict } from "@/utils/index.js";
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue';

// 标签页配置
const tabs = ref([
	{ name: '咨询师', type: 'consultant' },
	{ name: '课程', type: 'course' },
	{ name: '冥想', type: 'meditation' },
	{ name: '测评', type: 'assessment' }
]);

const currentTab = ref(0);
const counselorList = ref([]);
const courseList = ref([]);
const meditationList = ref([]);
const assessmentList = ref([]);
const psy_consultant_level = ref([]);

// 获取收藏列表
const loadFavoriteList = async () => {
	try {
		const currentTabType = tabs.value[currentTab.value].type;
		let targetType;

		switch (currentTabType) {
			case 'consultant':
				targetType = FAVORITE_TYPES.CONSULTANT;
				break;
			case 'course':
				targetType = FAVORITE_TYPES.COURSE;
				break;
			case 'meditation':
				targetType = FAVORITE_TYPES.MEDITATION;
				break;
			case 'assessment':
				targetType = FAVORITE_TYPES.ASSESSMENT;
				break;
		}

		const res = await getFavoriteList({ targetType });

		if (res.code === 200) {
			const favoriteData = res.data || [];

			switch (currentTabType) {
				case 'consultant':
					// 咨询师收藏 - 适配UniversalListItem组件
					counselorList.value = favoriteData.map(item => ({
						id: item.targetId,
						counselorId: item.targetId,
						favoriteId: item.favoriteId,
						name: item.actualTitle || item.targetTitle,
						title: item.actualTitle || item.targetTitle,
						image: item.actualImage || item.targetImage,
						avatar: item.actualImage || item.targetImage,
						counselorLevel: item.targetInfo?.level,
						level: item.targetInfo?.level,
						consultStyles: item.targetInfo?.consultStyles || [],
						specialties: item.targetInfo?.consultStyles || [],
						price: item.targetInfo?.price || 0,
						personalIntro: item.targetInfo?.personalIntro,
						description: item.targetInfo?.personalIntro,
						startYear: item.targetInfo?.startYear,
						serviceCount: item.targetInfo?.serviceCount,
						province: item.targetInfo?.province,
						city: item.targetInfo?.city,
						district: item.targetInfo?.district,
						address: item.targetInfo?.address,
						...item.targetInfo
					}));
					break;
				case 'course':
					// 课程收藏 - 适配UniversalListItem组件
					courseList.value = favoriteData.map(item => ({
						id: item.target_id,
						productId: item.target_id,
						favoriteId: item.favorite_id,
						name: item.actual_title || item.target_title,
						title: item.actual_title || item.target_title,
						image: item.actual_image || item.target_image,
						coverImage: item.actual_image || item.target_image,
						price: item.price || '0.00',
						originalPrice: item.price,
						chapterCount: item.chapter_count,
						difficultyLevel: item.difficulty_level,
						durationTotal: item.duration_total,
						isFree: item.is_free,
						ratingAvg: item.rating_avg,
						ratingCount: item.rating_count,
						salesCount: item.sales_count,
						viewCount: item.view_count_target,
						subtitle: item.subtitle,
						tags: item.tags,
						description: item.description || `${item.chapter_count || 0}章节 | ${item.subtitle || ''}`,
						serviceDirection: item.description
					}));
					break;
				case 'meditation':
					// 冥想收藏 - 适配UniversalListItem组件
					meditationList.value = favoriteData.map(item => ({
						id: item.targetId,
						meditationId: item.targetId,
						favoriteId: item.favoriteId,
						name: item.actualTitle || item.targetTitle,
						title: item.actualTitle || item.targetTitle,
						image: item.actualImage || item.targetImage,
						coverImage: item.actualImage || item.targetImage,
						price: item.targetInfo?.price || 0,
						originalPrice: item.targetInfo?.originalPrice,
						duration: item.targetInfo?.duration,
						categoryName: item.targetInfo?.categoryName,
						description: item.targetInfo?.description || item.targetInfo?.categoryName,
						...item.targetInfo
					}));
					break;
				case 'assessment':
					// 测评收藏 - 适配UniversalListItem组件
					assessmentList.value = favoriteData.map(item => ({
						id: item.targetId,
						assessmentId: item.targetId,
						favoriteId: item.favoriteId,
						name: item.actualTitle || item.targetTitle,
						title: item.actualTitle || item.targetTitle,
						scaleName: item.actualTitle || item.targetTitle,
						image: item.actualImage || item.targetImage,
						coverImage: item.actualImage || item.targetImage,
						price: item.targetInfo?.price || 0,
						originalPrice: item.targetInfo?.originalPrice,
						questionCount: item.targetInfo?.questionCount,
						categoryName: item.targetInfo?.categoryName,
						description: item.targetInfo?.description || `${item.targetInfo?.questionCount || 0}题 | ${item.targetInfo?.categoryName || ''}`,
						...item.targetInfo
					}));
					break;
			}
		}
	} catch (error) {
		console.error('获取收藏列表失败:', error);
		uni.showToast({
			title: '获取收藏列表失败',
			icon: 'none'
		});
	}
};

// 切换标签
const switchTab = (index) => {
	if (currentTab.value !== index) {
		currentTab.value = index;
		loadFavoriteList(); // 切换标签时重新获取列表
	}
};

// 跳转到咨询师详情
const handleCounselorDetail = (item) => {
	uni.navigateTo({
		url: `/pages/classification/counselor-detail/index?id=${item.counselorId}`
	});
};

// 跳转到课程详情
const handleCourseDetail = (item) => {
	uni.navigateTo({
		url: `/pages/course/detail/index?courseId=${item.productId}`
	});
};

// 跳转到冥想详情
const handleMeditationDetail = (item) => {
	uni.navigateTo({
		url: `/pages/meditation/detail/index?meditationId=${item.meditationId}`
	});
};

// 跳转到测评详情
const handleAssessmentDetail = (item) => {
	uni.navigateTo({
		url: `/pages/evaluation/detail/index?assessmentId=${item.assessmentId}`
	});
};

// 获取咨询师等级字典
const getDict = async () => {
	try {
		const { psy_consultant_level: dictData } = await useDict("psy_consultant_level");
		psy_consultant_level.value = dictData || [];
	} catch (error) {
		console.error('获取咨询师等级字典失败:', error);
	}
};

// 保留字典获取函数，供UniversalListItem使用
const getPsy_consultant_level = (value) => {
	return psy_consultant_level.value.find((item) => item.dictValue == value)?.dictLabel || "";
};

onLoad(() => {
	getDict();
	loadFavoriteList();
});
</script>

<style lang="scss" scoped>
.content {
	background-color: #f5f5f6;
	min-height: 100vh;

	// 搜索结果分类标签样式
	.result-category-tabs {
		display: flex;
		background: #fff;
		padding: 32rpx;
		padding-top: 47rpx;
		padding-bottom: 28rpx;
		justify-content: space-between;

		// border-bottom: 1px solid #eee;
		.tab-item {
			position: relative;
			// padding: 24rpx 32rpx;
			font-size: 28rpx;
			color: #8A8788;
			position: relative;
			margin-right: 140rpx;

			&.active {
				font-size: 32rpx;
				color: #A04571;
				display: flex;
				flex-direction: column;
				align-items: center;

				.tab-icon {
					width: 28rpx;
					height: 12rpx;
					margin-top: 10rpx;
				}
			}
		}

		.tab-item:last-child {
			margin-right: 0;
		}

		.tab-icon {
			width: 26rpx;
			height: 26rpx;
		}
	}

	.list-content {
		padding: 20rpx;
		background: #fff;
	}

	.empty-tip {
		text-align: center;
		color: #999;
		padding: 40rpx 0;
	}
}
</style>
