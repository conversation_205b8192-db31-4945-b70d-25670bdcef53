<template>
	<view class="test-container">
		<view class="header">
			<text class="title">订单列表组件测试</text>
		</view>

		<view class="section">
			<text class="section-title">咨询订单示例</text>
			<OrderListItem :item="consultantOrder" type="consultant" @click="handleItemClick" @action="handleAction" />
		</view>

		<view class="section">
			<text class="section-title">测评订单示例</text>
			<OrderListItem :item="assessmentOrder" type="assessment" @click="handleItemClick" @action="handleAction" />
		</view>

		<view class="section">
			<text class="section-title">冥想订单示例</text>
			<OrderListItem :item="meditationOrder" type="meditation" @click="handleItemClick" @action="handleAction" />
		</view>

		<view class="section">
			<text class="section-title">课程订单示例</text>
			<OrderListItem :item="courseOrder" type="course" @click="handleItemClick" @action="handleAction" />
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import OrderListItem from '@/components/OrderListItem/OrderListItem.vue'

// 测试数据
const consultantOrder = ref({
	id: 1,
	consultantName: '张艺涵',
	consultantAvatar: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
	status: 'pending',
	consultationType: '到店咨询',
	counselorLevel: '高级咨询师',
	appointmentTime: '07/08 14:00-15:00',
	actualPrice: '29.9',
	type: 'consultant'
})

const assessmentOrder = ref({
	id: 2,
	assessmentName: '性格测试',
	assessmentCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png',
	status: 'completed',
	assessmentType: '心理测评',
	estimatedTime: 20,
	createTime: '07/08 14:00',
	actualPrice: '19.9',
	type: 'assessment'
})

const meditationOrder = ref({
	id: 3,
	meditationName: '放松冥想',
	meditationCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
	status: 'in_progress',
	duration: 15,
	createTime: '07/08 14:00',
	actualPrice: '9.9',
	type: 'meditation'
})

const courseOrder = ref({
	id: 4,
	courseName: '情绪管理课程',
	courseCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
	status: 'refunding',
	courseType: '心理课程',
	chapters: 12,
	createTime: '07/08 14:00',
	actualPrice: '99.9',
	type: 'course'
})

// 事件处理
const handleItemClick = (item) => {
	console.log('点击订单:', item)
	uni.showToast({
		title: `点击了${item.consultantName || item.assessmentName || item.meditationName || item.courseName}`,
		icon: 'none'
	})
}

const handleAction = ({ action, item }) => {
	console.log('订单操作:', action, item)
	uni.showToast({
		title: `执行操作: ${action}`,
		icon: 'none'
	})
}
</script>

<style lang="scss" scoped>
.test-container {
	padding: 32rpx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 32rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section {
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 16rpx;
	display: block;
}
</style>
