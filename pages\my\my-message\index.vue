<template>
  <view class="message-container">
    <!-- <view class="header">
      <text class="title">我的消息</text>
    </view> -->
    <view class="result-category-tabs">
      <view v-for="(tab, index) in tabList" :key="tab.key" :class="['tab-item', { active: currentTab === tab.key }]"
        @click="switchTab(tab.key)">
        <text class="tab-name">{{ tab.name }}</text>
        <!-- <text class="tab-count" v-if="getTabCount(tab.key) > 0">({{ getTabCount(tab.key) }})</text> -->
        <image v-if="currentTab === tab.key" class="tab-icon" src="../../../static/icon/形状 6-active.png"></image>
      </view>
    </view>
    <view class="empty-state" v-if="conversationList.length === 0">
      <!-- <image src="/static/icon/empty-message.png" mode="aspectFit"></image> -->
      <text>暂无消息</text>
    </view>

    <view class="conversation-list" v-else>
      <view class="conversation-item" v-for="item in conversationList" :key="item.conversationId"
        @click="goToChat(item)">
        <view class="avatar-container">
          <image class="avatar" :src="item.consultantAvatar || '/static/icon/default-avatar.png'" mode="aspectFill">
          </image>
        </view>
        <view class="conversation-info">
          <view class="top-row">
            <text class="nickname">{{ item.counselorName || item.consultantName || '咨询师' }}</text>
            <text class="time">{{ formatTime(item.lastMessageTime) }}</text>
          </view>
          <view class="bottom-row">
            <text class="last-message" :class="{ 'message-withdrawn': isWithdrawn(item.lastMessage) }">
              {{ formatLastMessage(item) }}
            </text>
            <view class="badge" v-if="getUnreadCount(item) > 0">{{ getUnreadCount(item) }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useChatStore } from '@/stores/chat';
import { useUserStore } from '@/stores/user';
import { onShow } from '@dcloudio/uni-app';

const chatStore = useChatStore();
const userStore = useUserStore();
const isConsultant = ref(false);

// 当前选中的tab
const currentTab = ref('all');

// 分类标签配置
const tabList = [
  { key: 'all', name: '全部' },
  { key: 'consultation', name: '咨询消息' },
  { key: 'system', name: '系统消息' }
];

// 根据当前tab过滤会话列表
const conversationList = computed(() => {
  const allConversations = chatStore.conversationList || [];

  if (currentTab.value === 'all') {
    return allConversations;
  } else if (currentTab.value === 'consultation') {
    // 过滤咨询消息（有咨询师的会话）
    return allConversations.filter(item => {
      // 判断是否是咨询会话：有咨询师ID或咨询师姓名
      return item.consultantId ||
        item.counselorName ||
        item.consultantName ||
        item.consultantAvatar;
    });
  } else if (currentTab.value === 'system') {
    // 过滤系统消息（没有咨询师的会话，通常是系统通知类消息）
    return allConversations.filter(item => {
      // 判断是否是系统消息：没有咨询师相关信息
      return !item.consultantId &&
        !item.counselorName &&
        !item.consultantName &&
        !item.consultantAvatar;
    });
  }

  return allConversations;
});

// 切换tab
const switchTab = (tabKey) => {
  currentTab.value = tabKey;
  console.log('切换到tab:', tabKey);
};

// 计算各个tab的消息数量
const getTabCount = (tabKey) => {
  const allConversations = chatStore.conversationList || [];

  if (tabKey === 'all') {
    return allConversations.length;
  } else if (tabKey === 'consultation') {
    return allConversations.filter(item => {
      return item.consultantId ||
        item.counselorName ||
        item.consultantName ||
        item.consultantAvatar;
    }).length;
  } else if (tabKey === 'system') {
    return allConversations.filter(item => {
      return !item.consultantId &&
        !item.counselorName &&
        !item.consultantName &&
        !item.consultantAvatar;
    }).length;
  }

  return 0;
};
// 页面加载时初始化数据
onMounted(async () => {
  console.log('用户消息页面加载，用户信息:', userStore.profile)
  console.log('用户ID:', userStore.userId)

  // 确保WebSocket连接
  if (userStore.userId && (!chatStore.wsConnected || !uni.webSocketTask)) {
    console.log('初始化用户WebSocket连接')
    chatStore.initWebSocket(userStore.userId)
  }

  await loadConversations();
});

// 每次显示页面时刷新数据
onShow(async () => {
  console.log('用户消息页面显示，用户信息:', userStore.profile)
  console.log('用户ID:', userStore.userId)

  // 确保WebSocket连接
  if (userStore.userId && (!chatStore.wsConnected || !uni.webSocketTask)) {
    console.log('重新初始化用户WebSocket连接')
    chatStore.initWebSocket(userStore.userId)
  }

  // 清除未读消息计数（用户进入消息页面表示已查看）
  chatStore.unreadTotal = 0;
  uni.$emit('unread-count-changed', 0);
  console.log('进入消息页面，清除未读计数');

  await loadConversations();
});

// 加载会话列表
const loadConversations = async () => {
  // 根据用户角色加载不同的会话列表
  await chatStore.getConversationList(isConsultant.value);
  console.log('用户端会话列表:', conversationList.value)
  await chatStore.getUnreadCount();
};

// 获取未读消息数量
const getUnreadCount = (conversation) => {
  if (isConsultant.value) {
    return conversation.consultantUnreadCount || 0;
  } else {
    return conversation.userUnreadCount || 0;
  }
};

// 格式化最后消息时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';

  const messageDate = new Date(timestamp);
  const now = new Date();

  // 同一天显示时间
  if (messageDate.toDateString() === now.toDateString()) {
    return messageDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }

  // 一周内显示星期几
  const dayDiff = Math.floor((now - messageDate) / (1000 * 60 * 60 * 24));
  if (dayDiff < 7) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return weekdays[messageDate.getDay()];
  }

  // 超过一周显示日期
  return messageDate.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
};

// 判断消息是否已撤回
const isWithdrawn = (message) => {
  if (!message) return false;
  try {
    // 尝试解析JSON，判断是否是撤回消息
    const parsedMessage = JSON.parse(message);
    return parsedMessage.type === 'withdraw_notification';
  } catch (e) {
    // 不是JSON格式，返回false
    return false;
  }
};

// 格式化最后一条消息
const formatLastMessage = (conversation) => {
  if (!conversation.lastMessage) return '';

  try {
    // 尝试解析JSON，处理系统消息
    const parsedMessage = JSON.parse(conversation.lastMessage);
    if (parsedMessage.type === 'withdraw_notification') {
      return '消息已撤回';
    }
    return parsedMessage.content || '';
  } catch (e) {
    // 不是JSON格式，直接返回
    return conversation.lastMessage;
  }
};


// 跳转到聊天页面
const goToChat = (conversation) => {
  uni.navigateTo({
    url: `/pages/my/my-message/chat/index?conversationId=${conversation.conversationId}&userId=${userStore.profile.userId}&consultantId=${conversation.consultantId}&nickname=${conversation.counselorName}&consultantAvatar=${conversation.consultantAvatar}`
  });
};
</script>

<style scoped lang="scss">
.message-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 24rpx);
  padding-top: 24rpx;

  // 搜索结果分类标签样式
  .result-category-tabs {
    display: flex;
    background: #fff;
    padding: 32rpx;
    padding-top: 47rpx;
    padding-bottom: 28rpx;
    justify-content: space-between;

    // border-bottom: 1px solid #eee;
    .tab-item {
      position: relative;
      // padding: 24rpx 32rpx;
      font-size: 28rpx;
      color: #8A8788;
      position: relative;
      margin-right: 148rpx;
      display: flex;
      flex-direction: column;
      align-items: center;

      .tab-name {
        font-size: 28rpx;
        color: inherit;
      }

      .tab-count {
        font-size: 20rpx;
        color: #999;
        margin-top: 4rpx;
      }

      &.active {
        .tab-name {
          font-size: 32rpx;
          color: #A04571;
          font-weight: 500;
        }

        .tab-count {
          color: #A04571;
        }

        .tab-icon {
          width: 28rpx;
          height: 12rpx;
          margin-top: 10rpx;
        }
      }
    }

    .tab-item:last-child {
      margin-right: 0;
    }

    .tab-icon {
      width: 26rpx;
      height: 26rpx;
    }
  }

  .header {
    padding: 20rpx 0;
    margin-bottom: 20rpx;

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60vh;

    image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }

    text {
      color: #999;
      font-size: 28rpx;
    }
  }

  .conversation-list {
    .conversation-item {
      display: flex;
      padding: 32rpx;
      background-color: #fff;
      border-radius: 20rpx;
      position: relative;

      .avatar-container {
        position: relative;
        margin-right: 15rpx;

        .avatar {
          width: 92rpx;
          height: 92rpx;
          border-radius: 8rpx;
        }
      }

      .conversation-info {
        flex: 1;
        overflow: hidden;

        .top-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10rpx;

          .nickname {
            font-size: 30rpx;
            font-weight: 700;
            color: #000;
          }

          .time {
            font-size: 20rpx;
            color: #8B8788;
          }
        }

        .bottom-row {
          display: flex;
          justify-content: space-between;

          // border-bottom: 2rpx solid #E7E7E7;
          &::after {
            content: '';
            width: 100%;
            height: 2rpx;
            background-color: #E7E7E7;
            position: absolute;
            bottom: 0;
            left: 0;
          }

          .badge {
            background-color: #A04571;
            color: #fff;
            font-size: 20rpx;
            min-width: 40rpx;
            height: 40rpx;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .last-message {
            font-size: 24rpx;
            color: #ACA8AA;
            // @include text-ellipsis;
            max-width: 550rpx;
            display: inline-block;
          }

          .message-withdrawn {
            color: #999;
            font-style: italic;
          }
        }
      }

      &:active {
        background-color: #f0f0f0;
      }
    }
  }
}
</style>
