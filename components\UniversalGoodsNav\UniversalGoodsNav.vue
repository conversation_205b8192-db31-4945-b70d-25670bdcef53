<template>
  <view class="universal-goods-nav">
    <!-- 底部占位 -->
    <view class="nav-seat" />
    <view class="nav-container">
      <!-- 左侧操作按钮 -->
      <view class="nav-left">
        <view v-for="(item, index) in leftOptions" :key="index" class="nav-button"
          @click="handleLeftClick(index, item)">
          <view class="nav-icon">
            <image :src="item.icon" mode="widthFix" class="icon-image" />
          </view>
          <text class="nav-text">{{ item.text }}</text>
          <view v-if="item.info" class="nav-badge">
            <text class="badge-text">{{ item.info > 99 ? '99+' : item.info }}</text>
          </view>
        </view>
      </view>

      <!-- 右侧按钮组 -->
      <view class="nav-right">
        <view v-for="(button, index) in rightButtons" :key="index" class="nav-main-button"
          :style="{ background: button.backgroundColor, color: button.color }" @click="handleRightClick(index, button)">
          <text>{{ button.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import {
  addFavorite,
  removeFavorite,
  checkFavorite,
  buildFavoriteData,
  getTargetTypeByPage
} from '@/api/favorite.js'

// Props
const props = defineProps({
  // 页面类型：assessment, meditation, course, counselor
  pageType: {
    type: String,
    required: true,
    validator: (value) => ['assessment', 'meditation', 'course', 'counselor'].includes(value)
  },
  // 详情数据
  detailData: {
    type: Object,
    default: () => ({})
  },
  // 是否已购买/已完成
  purchased: {
    type: Boolean,
    default: false
  },
  // 测评状态（用于测评页面）
  assessmentStatus: {
    type: String,
    default: ''
  },
  // 价格
  price: {
    type: [Number, String],
    default: 0
  },
  // 收藏状态
  favorited: {
    type: Boolean,
    default: false
  },
  // 收藏ID
  favoriteId: {
    type: [String, Number],
    default: null
  }
})

// Emits
const emit = defineEmits(['favorite', 'contact-service', 'share', 'main-action'])

// Store
const userStore = useUserStore()

// 响应式数据
const isFavorited = ref(props.favorited)
const currentFavoriteId = ref(props.favoriteId)

// 计算属性 - 左侧选项
const leftOptions = computed(() => {
  const options = [
    {
      icon: '../../static/icon/组 33.png',
      text: '分享',
      info: 0
    },
    {
      icon: '../../static/icon/客服.png',
      text: '客服',
      info: 0
    },
    {
      icon: isFavorited.value ? 'star-filled' : '../../static/icon/形状 6(1).png',
      text: '收藏',
      info: 0
    }
  ]
  return options
})

// 计算属性 - 右侧按钮
const rightButtons = computed(() => {
  switch (props.pageType) {
    case 'assessment':
      // 根据测评状态显示不同按钮
      if (props.purchased) {
        return [{
          text: '查看报告',
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      } else if (props.assessmentStatus === '进行中' || props.assessmentStatus === '暂停中') {
        return [{
          text: '继续测评',
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      } else if (props.detailData.isFree === 1 || props.price == 0) {
        return [{
          text: '免费测评',
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      } else {
        return [{
          text: `开始测评 ¥${props.price}`,
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      }

    case 'meditation':
      if (props.purchased) {
        return [{
          text: '开始冥想',
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      } else if (props.detailData.isFree === 1 || props.price == 0) {
        return [{
          text: '免费冥想',
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      } else {
        return [{
          text: `立即购买 ¥${props.price}`,
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      }

    case 'course':
      if (props.purchased) {
        return [{
          text: '开始学习',
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      } else if (props.detailData.isFree === 1 || props.price == 0) {
        return [{
          text: '免费学习',
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      } else {
        return [{
          text: `立即购买 ¥${props.price}`,
          backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
          color: '#fff'
        }]
      }

    case 'counselor':
      return [{
        text: '预约咨询',
        backgroundColor: "linear-gradient(90deg, #A04571, #923C65)",
        color: '#fff'
      }]

    default:
      return []
  }
})

// 方法
// 处理左侧按钮点击
const handleLeftClick = async (index, item) => {
  if (index === 0) {
    // 分享功能
    handleShare()
  } else if (index === 1) {
    // 客服功能
    await handleContactService()
  } else if (index === 2) {
    // 收藏功能
    await handleFavorite()
  }
}

// 处理右侧按钮点击
const handleRightClick = (index, button) => {
  emit('main-action', { index, button, pageType: props.pageType })
}

// 收藏功能
const handleFavorite = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    if (isFavorited.value) {
      // 取消收藏
      if (currentFavoriteId.value) {
        const res = await removeFavorite(currentFavoriteId.value)
        if (res.code === 200) {
          isFavorited.value = false
          currentFavoriteId.value = null

          uni.showToast({
            title: '取消收藏成功',
            icon: 'success'
          })
        } else {
          throw new Error(res.msg || '取消收藏失败')
        }
      }
    } else {
      // 添加收藏
      const favoriteData = buildFavoriteData(
        props.pageType,
        getTargetId(),
        props.detailData || {}
      )

      const res = await addFavorite(favoriteData)
      if (res.code === 200) {
        isFavorited.value = true
        currentFavoriteId.value = res.data?.favoriteId

        uni.showToast({
          title: '收藏成功',
          icon: 'success'
        })
      } else {
        throw new Error(res.msg || '收藏失败')
      }
    }

    emit('favorite', {
      favorited: isFavorited.value,
      favoriteId: currentFavoriteId.value
    })
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: error.message || '操作失败',
      icon: 'none'
    })
  }
}

// 获取目标类型
const getTargetType = () => {
  return getTargetTypeByPage(props.pageType)
}

// 获取目标ID
const getTargetId = () => {
  return props.detailData.id || props.detailData.assessmentId || props.detailData.courseId || props.detailData.counselorId
}

// 客服功能
const handleContactService = async () => {
  try {
    // 固定客服ID为137
    const customerServiceId = "137"
    // const { data } = await createConversation(customerServiceId)
    const conversationId = data?.conversationId || customerServiceId

    uni.navigateTo({
      url: `/pages/my/my-message/chat/chat?conversationId=${conversationId}&userId=${userStore.userId}&consultantId=${customerServiceId}&nickname=熙桓心理客服`
    })

    emit('contact-service')
  } catch (error) {
    console.error("创建会话失败", error)
    uni.showToast({
      title: "连接客服失败",
      icon: "none"
    })
  }
}

// 转发功能
const handleShare = () => {
  const shareConfig = getShareConfig()

  // #ifdef MP-WEIXIN
  // 在微信小程序中，需要通过页面的 onShareAppMessage 来实现转发
  uni.$emit('triggerShare', shareConfig)
  uni.showToast({
    title: '请点击右上角分享',
    icon: 'none'
  })
  // #endif

  // #ifdef H5
  // H5环境下使用Web Share API或复制链接
  if (navigator.share) {
    navigator.share({
      title: shareConfig.title,
      url: window.location.origin + '/#/' + shareConfig.path
    }).catch(err => {
      console.log('分享失败:', err)
      copyToClipboard(window.location.origin + '/#/' + shareConfig.path)
    })
  } else {
    copyToClipboard(window.location.origin + '/#/' + shareConfig.path)
  }
  // #endif

  emit('share', shareConfig)
}

// 获取分享配置
const getShareConfig = () => {
  const baseConfig = {
    title: props.detailData.title || '熙桓心理',
    imageUrl: props.detailData.coverImage || props.detailData.imageUrl || ''
  }

  switch (props.pageType) {
    case 'assessment':
      return {
        ...baseConfig,
        title: `推荐测评：${props.detailData.scaleName || props.detailData.name || props.detailData.title}`,
        path: `pages/evaluation/detail/index?id=${props.detailData.id}`,
        summary: props.detailData.description || '专业心理测评，了解自己'
      }

    case 'meditation':
      return {
        ...baseConfig,
        title: `推荐冥想：${props.detailData.title}`,
        path: `pages/meditation/detail/index?id=${props.detailData.id}`,
        summary: props.detailData.description || '放松身心，冥想练习'
      }

    case 'course':
      return {
        ...baseConfig,
        title: `推荐课程：${props.detailData.title}`,
        path: `pages/course/detail/index?id=${props.detailData.id}`,
        summary: props.detailData.description || '专业心理课程，提升自我'
      }

    case 'counselor':
      return {
        ...baseConfig,
        title: `推荐咨询师：${props.detailData.name}`,
        path: `pages/classification/counselor-detail/index?id=${props.detailData.id}`,
        summary: props.detailData.introduction || '专业心理咨询师'
      }

    default:
      return {
        title: '熙桓心理',
        path: 'pages/index/index',
        imageUrl: '',
        summary: '专业心理服务平台'
      }
  }
}

// H5环境下的复制到剪贴板功能
const copyToClipboard = (text) => {
  // #ifdef H5
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      })
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    uni.showToast({
      title: '链接已复制',
      icon: 'success'
    })
  }
  // #endif
}

// 监听props变化
import { watch } from 'vue'

watch(() => props.favorited, (newVal) => {
  isFavorited.value = newVal
})

watch(() => props.favoriteId, (newVal) => {
  currentFavoriteId.value = newVal
})
</script>

<style lang="scss" scoped>
.universal-goods-nav {
  .nav-seat {
    height: 120rpx;
  }

  .nav-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    background-color: #fff;
    border-top: 1rpx solid #eee;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    z-index: 1;

    // 安全区域适配
    padding-bottom: env(safe-area-inset-bottom);

    .nav-left {
      display: flex;
      // flex: 1;

      .nav-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        // padding: 0 20rpx;
        position: relative;
        min-width: 80rpx;

        .nav-icon {
          margin-bottom: 4rpx;

          .icon-image {
            width: 38rpx;
            height: 38rpx;
          }
        }

        .nav-text {
          font-size: 20rpx;
          color: #646566;
        }

        .nav-badge {
          position: absolute;
          top: -5rpx;
          right: 10rpx;
          background-color: #ff0000;
          border-radius: 20rpx;
          min-width: 32rpx;
          height: 52rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .badge-text {
            font-size: 20rpx;
            color: #fff;
            padding: 0 8rpx;
          }
        }
      }
    }

    .nav-right {
      display: flex;
      gap: 20rpx;
      // flex: 1;
      width: 100%;

      .nav-main-button {
        height: 90rpx;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 40rpx;
        width: 100%;
        font-size: 32rpx;
        font-weight: 500;

        .button-text {
          color: inherit;
        }
      }
    }
  }
}

// 深色模式适配已移除，因为微信小程序不支持@media查询</style>
