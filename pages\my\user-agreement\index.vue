<template>
	<view class="agreement-container">
		<view class="content">
			<view class="title">用户协议</view>
			
			<view class="section">
				<view class="section-title">1. 服务条款的确认和接纳</view>
				<view class="section-content">
					欢迎使用熙桓心理服务！本协议是您与熙桓心理之间关于您使用熙桓心理服务所订立的协议。请您仔细阅读本协议，您点击"同意"、"下一步"或您的注册、使用等行为或者以其他任何明示或者默示方式表示接受本协议的，即视为您已阅读并同意本协议的约束。
				</view>
			</view>

			<view class="section">
				<view class="section-title">2. 服务内容</view>
				<view class="section-content">
					熙桓心理为用户提供心理咨询、心理测评、冥想课程等相关服务。具体服务内容以平台实际提供为准。
				</view>
			</view>

			<view class="section">
				<view class="section-title">3. 用户注册</view>
				<view class="section-content">
					用户在使用本服务前需要注册一个账户。用户应当使用真实、准确、完整的信息进行注册，并及时更新注册信息。用户不得以虚假信息注册账户，不得恶意注册账户。
				</view>
			</view>

			<view class="section">
				<view class="section-title">4. 用户行为规范</view>
				<view class="section-content">
					用户在使用服务时应当遵守法律法规，不得利用本服务从事违法违规活动。用户应当文明使用服务，不得发布违法、有害、威胁、辱骂、骚扰、侵权、中伤、粗俗、猥亵或其他道德上令人反感的内容。
				</view>
			</view>

			<view class="section">
				<view class="section-title">5. 隐私保护</view>
				<view class="section-content">
					我们重视用户隐私保护，将按照相关法律法规和隐私政策处理用户个人信息。用户的咨询内容和个人信息将得到严格保密。
				</view>
			</view>

			<view class="section">
				<view class="section-title">6. 知识产权</view>
				<view class="section-content">
					本服务中的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受知识产权法律保护。未经授权，用户不得复制、传播、展示、镜像、上传、下载使用上述内容。
				</view>
			</view>

			<view class="section">
				<view class="section-title">7. 免责声明</view>
				<view class="section-content">
					本服务仅提供信息交流平台，不对咨询效果做任何保证。用户应当理性对待咨询建议，如有严重心理问题应及时寻求专业医疗帮助。
				</view>
			</view>

			<view class="section">
				<view class="section-title">8. 协议修改</view>
				<view class="section-content">
					我们有权根据需要修改本协议条款。协议修改后，我们会在平台上公布修改后的协议。如果您不同意修改后的协议，可以停止使用服务。
				</view>
			</view>

			<view class="section">
				<view class="section-title">9. 联系我们</view>
				<view class="section-content">
					如果您对本协议有任何疑问，请通过平台客服联系我们。
				</view>
			</view>

			<view class="footer">
				<text class="update-time">最后更新时间：2024年1月1日</text>
			</view>
		</view>
	</view>
</template>

<script setup>
// 页面逻辑
</script>

<style scoped lang="scss">
.agreement-container {
	background-color: #fff;
	min-height: 100vh;
}

.content {
	padding: 32rpx;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 48rpx;
}

.section {
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
}

.section-content {
	font-size: 28rpx;
	line-height: 1.6;
	color: #666;
	text-indent: 2em;
}

.footer {
	margin-top: 64rpx;
	padding-top: 32rpx;
	border-top: 1rpx solid #f0f0f0;
	text-align: center;
}

.update-time {
	font-size: 24rpx;
	color: #999;
}
</style>
