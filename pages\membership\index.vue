<template>
	<view class="membership-center">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="20" color="#fff"></uni-icons>
			</view>
			<view class="nav-title">会员中心</view>
			<view class="nav-right">
				<uni-icons type="more-filled" size="20" color="#fff"></uni-icons>
				<uni-icons type="help" size="20" color="#fff" style="margin-left: 16rpx;"></uni-icons>
			</view>
		</view>

		<!-- 用户信息卡片 -->
		<view class="user-card">
			<view class="user-info">
				<image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
				<view class="user-details">
					<text class="username">{{ userInfo.nickname || '旧城' }}</text>
					<text class="user-id">会员有效期至2025-09-12到期</text>
				</view>
			</view>
			<view class="member-badge">
				<text class="badge-text">会员续费</text>
			</view>
		</view>

		<!-- 会员权益 -->
		<view class="member-benefits">
			<view class="section-title">会员权益</view>
			<view class="benefits-grid">
				<view class="benefit-item">
					<view class="benefit-icon">
						<uni-icons type="chat" size="30" color="#D4749A"></uni-icons>
					</view>
					<text class="benefit-text">免费咨询</text>
				</view>
				<view class="benefit-item">
					<view class="benefit-icon">
						<uni-icons type="compose" size="30" color="#D4749A"></uni-icons>
					</view>
					<text class="benefit-text">免费测试</text>
				</view>
				<view class="benefit-item">
					<view class="benefit-icon">
						<uni-icons type="heart" size="30" color="#D4749A"></uni-icons>
					</view>
					<text class="benefit-text">免费冥想</text>
				</view>
				<view class="benefit-item">
					<view class="benefit-icon">
						<uni-icons type="contact" size="30" color="#D4749A"></uni-icons>
					</view>
					<text class="benefit-text">专属客服</text>
				</view>
			</view>
		</view>

		<!-- 会员套餐 -->
		<view class="membership-plans">
			<view class="section-title">会员套餐</view>
			<view class="plans-container">
				<!-- 月卡 -->
				<view class="plan-card" :class="{ 'selected': selectedPlan === 'monthly' }" @click="selectPlan('monthly')">
					<view class="plan-header">
						<text class="plan-label">申请续费</text>
						<text class="plan-title">月卡</text>
					</view>
					<view class="plan-price">
						<text class="currency">¥</text>
						<text class="price">9.9</text>
					</view>
					<text class="plan-desc">首月特惠</text>
				</view>

				<!-- 季卡 -->
				<view class="plan-card" :class="{ 'selected': selectedPlan === 'quarterly' }" @click="selectPlan('quarterly')">
					<view class="plan-header">
						<text class="plan-title">季卡</text>
					</view>
					<view class="plan-price">
						<text class="currency">¥</text>
						<text class="price">48.9</text>
					</view>
					<text class="plan-desc">季度优惠</text>
				</view>

				<!-- 年卡 -->
				<view class="plan-card" :class="{ 'selected': selectedPlan === 'yearly' }" @click="selectPlan('yearly')">
					<view class="plan-header">
						<text class="plan-title">年卡</text>
					</view>
					<view class="plan-price">
						<text class="currency">¥</text>
						<text class="price">128</text>
					</view>
					<text class="plan-desc">年度超值</text>
				</view>
			</view>

			<view class="plan-note">
				<uni-icons type="info" size="14" color="#999"></uni-icons>
				<text class="note-text">到期自动续费，可随时取消</text>
			</view>
		</view>

		<!-- 会员说明 -->
		<view class="membership-description">
			<view class="section-title">会员说明</view>
			<view class="desc-content">
				<view class="desc-item">
					<text class="desc-title">1.活动期间所有会员共享核心权益</text>
					<text
						class="desc-text">包含会员、智能匹配、专业咨询师、无限制对话、专属客服等权益。智能匹配开放全部咨询师心理健康，两对一服务等专业心理咨询；无限制对话：不限次数和时长专业心理咨询；专属客服：专属客服一对一服务。</text>
				</view>

				<view class="desc-item">
					<text class="desc-title">2.年付会员(399元/年)专属权利</text>
					<text class="desc-text">一次付费，全年无忧，全年无限制咨询。
						年付会员可享受全年无限制专业心理咨询师服务(价值超过5000元年费会员服务)，可直接联系专属心理咨询师进行心理咨询服务。</text>
				</view>

				<view class="desc-item">
					<text class="desc-title">开通会员5.99元，手机端支付(微信/支付宝/苹果支付)支持</text>
					<text class="desc-text">年付会员可享受全年无限制专业心理咨询师服务。</text>
				</view>

				<view class="desc-item">
					<text class="desc-title">3.月付会员(低门槛高体验)</text>
					<text class="desc-text">标准月付：35元/月，开通即享全会员权益体验。
						自由月付：可自由选择，月付仅需26元(立省9元/月)，随时可取消。</text>
				</view>
			</view>
		</view>

		<!-- 底部支付按钮 -->
		<view class="bottom-payment">
			<view class="payment-btn" @click="handlePayment">
				<text class="btn-text">支付35元 开通月卡会员</text>
			</view>
			<view class="payment-btn secondary" @click="goToPayment">
				<text class="btn-text">去支付</text>
			</view>
		</view>

		<!-- 底部说明 -->
		<view class="bottom-note">
			<text class="note-text">请阅读并同意 </text>
			<text class="link-text" @click="showAgreement">会员权限及服务协议</text>
			<text class="note-text"> | </text>
			<text class="link-text" @click="showPrivacy">用户隐私</text>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 用户信息
const userInfo = ref({
	nickname: '旧城',
	avatar: '',
	memberId: '会员有效期至2025-09-12到期'
});

// 选中的套餐
const selectedPlan = ref('monthly');

// 选择套餐
const selectPlan = (plan) => {
	selectedPlan.value = plan;
};

// 返回上一页
const goBack = () => {
	uni.navigateBack();
};

// 处理支付
const handlePayment = () => {
	let price = '';
	let planName = '';

	switch (selectedPlan.value) {
		case 'monthly':
			price = '9.9';
			planName = '月卡';
			break;
		case 'quarterly':
			price = '48.9';
			planName = '季卡';
			break;
		case 'yearly':
			price = '128';
			planName = '年卡';
			break;
	}

	uni.showModal({
		title: '确认支付',
		content: `确认支付 ¥${price} 开通${planName}会员？`,
		success: (res) => {
			if (res.confirm) {
				// 跳转到支付页面
				uni.navigateTo({
					url: `/pages/payment/index?plan=${selectedPlan.value}&price=${price}&planName=${planName}`
				});
			}
		}
	});
};

// 去支付
const goToPayment = () => {
	handlePayment();
};

// 显示协议
const showAgreement = () => {
	uni.navigateTo({
		url: '/pages/my/user-agreement/index'
	});
};

// 显示隐私政策
const showPrivacy = () => {
	uni.navigateTo({
		url: '/pages/my/privacy-policy/index'
	});
};

// 页面加载
onMounted(() => {
	// 获取用户信息
	const userInfoStorage = uni.getStorageSync('userInfo');
	if (userInfoStorage) {
		userInfo.value = { ...userInfo.value, ...userInfoStorage };
	}
});
</script>

<style lang="scss" scoped>
.membership-center {
	min-height: 100vh;
	background: linear-gradient(180deg, #D4749A 0%, #F5F5F5 40%);

	// 顶部导航栏
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 32rpx;
		padding-top: calc(var(--status-bar-height) + 20rpx);

		.nav-left {
			width: 60rpx;
		}

		.nav-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #fff;
		}

		.nav-right {
			display: flex;
			align-items: center;
			width: 60rpx;
			justify-content: flex-end;
		}
	}

	// 用户信息卡片
	.user-card {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 20rpx 32rpx;
		padding: 32rpx;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

		.user-info {
			display: flex;
			align-items: center;

			.avatar {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50rpx;
				margin-right: 24rpx;
			}

			.user-details {
				display: flex;
				flex-direction: column;

				.username {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8rpx;
				}

				.user-id {
					font-size: 24rpx;
					color: #999;
				}
			}
		}

		.member-badge {
			padding: 12rpx 24rpx;
			background: #D4749A;
			border-radius: 20rpx;

			.badge-text {
				font-size: 24rpx;
				color: #fff;
				font-weight: 500;
			}
		}
	}

	// 会员权益
	.member-benefits {
		margin: 40rpx 32rpx;

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 24rpx;
		}

		.benefits-grid {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 20rpx;

			.benefit-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 24rpx;
				background: #fff;
				border-radius: 12rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				.benefit-icon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 60rpx;
					height: 60rpx;
					margin-bottom: 12rpx;
				}

				.benefit-text {
					font-size: 24rpx;
					color: #333;
					text-align: center;
				}
			}
		}
	}

	// 会员套餐
	.membership-plans {
		margin: 40rpx 32rpx;

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 24rpx;
		}

		.plans-container {
			display: flex;
			gap: 16rpx;
			margin-bottom: 20rpx;

			.plan-card {
				flex: 1;
				padding: 24rpx;
				background: #fff;
				border-radius: 12rpx;
				border: 2rpx solid #E5E5E5;
				text-align: center;
				position: relative;

				&.selected {
					border-color: #D4749A;
					background: linear-gradient(135deg, #FFF5F8 0%, #FFFFFF 100%);
				}

				.plan-header {
					margin-bottom: 16rpx;

					.plan-label {
						display: inline-block;
						padding: 4rpx 12rpx;
						background: #D4749A;
						color: #fff;
						font-size: 20rpx;
						border-radius: 8rpx;
						margin-bottom: 8rpx;
					}

					.plan-title {
						display: block;
						font-size: 28rpx;
						font-weight: 600;
						color: #333;
					}
				}

				.plan-price {
					display: flex;
					align-items: baseline;
					justify-content: center;
					margin-bottom: 12rpx;

					.currency {
						font-size: 24rpx;
						color: #D4749A;
						font-weight: 600;
					}

					.price {
						font-size: 48rpx;
						color: #D4749A;
						font-weight: 700;
						margin-left: 4rpx;
					}
				}

				.plan-desc {
					font-size: 24rpx;
					color: #999;
				}
			}
		}

		.plan-note {
			display: flex;
			align-items: center;
			justify-content: center;

			.note-text {
				font-size: 24rpx;
				color: #999;
				margin-left: 8rpx;
			}
		}
	}

	// 会员说明
	.membership-description {
		margin: 40rpx 32rpx;

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 24rpx;
		}

		.desc-content {
			.desc-item {
				margin-bottom: 32rpx;

				.desc-title {
					display: block;
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 12rpx;
					line-height: 1.4;
				}

				.desc-text {
					font-size: 26rpx;
					color: #666;
					line-height: 1.6;
				}
			}
		}
	}

	// 底部支付按钮
	.bottom-payment {
		position: fixed;
		bottom: 120rpx;
		left: 32rpx;
		right: 32rpx;
		display: flex;
		gap: 16rpx;

		.payment-btn {
			flex: 1;
			padding: 24rpx;
			background: #333;
			border-radius: 12rpx;
			text-align: center;

			&.secondary {
				background: #D4749A;
			}

			.btn-text {
				font-size: 32rpx;
				color: #fff;
				font-weight: 600;
			}
		}
	}

	// 底部说明
	.bottom-note {
		position: fixed;
		bottom: 60rpx;
		left: 0;
		right: 0;
		text-align: center;
		padding: 0 32rpx;

		.note-text {
			font-size: 24rpx;
			color: #999;
		}

		.link-text {
			font-size: 24rpx;
			color: #D4749A;
		}
	}
}
</style>
