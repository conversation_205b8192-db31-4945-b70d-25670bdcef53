/**
 * 支付相关工具函数
 * 基于新的支付系统API
 */
import {
  createCourseOrder,
  createMeditationOrder,
  createConsultantOrder,
  createAssessmentOrder,
  payOrder,
  queryPayStatus,
  cancelOrder as cancelOrderAPI,
  requestRefund as requestRefundAPI,
  ORDER_TYPES
} from '@/api/payment.js'

/**
 * 微信支付参数版本检测和转换
 * @param {Object} payParams - 后端返回的支付参数
 * @returns {Object} - 标准化的支付参数
 */
function normalizeWxPayParams(payParams) {
  console.log('原始支付参数:', payParams)

  // 多种方式判断微信支付版本
  const isV2 = payParams.total_fee ||
    payParams.appId ||
    (payParams.signType && payParams.signType.toUpperCase() === 'MD5') ||
    payParams.mch_id ||
    payParams.nonce_str

  let normalizedParams = {}

  if (isV2) {
    console.log('检测到微信支付V2版本参数')
    // V2版本参数映射
    normalizedParams = {
      appId: payParams.appId,
      timeStamp: payParams.timeStamp || payParams.timestamp || String(Math.floor(Date.now() / 1000)),
      nonceStr: payParams.nonceStr || payParams.nonce_str,
      package: payParams.package || (payParams.prepay_id ? `prepay_id=${payParams.prepay_id}` : ''),
      signType: payParams.signType || 'MD5',
      paySign: payParams.paySign || payParams.sign
    }

    // V2版本必须包含appId
    if (!normalizedParams.appId) {
      throw new Error('微信支付V2版本缺少appId参数，请检查后端配置')
    }
  } else {
    console.log('检测到微信支付V3版本参数')
    // V3版本参数映射
    normalizedParams = {
      timeStamp: payParams.timeStamp || payParams.timestamp || String(Math.floor(Date.now() / 1000)),
      nonceStr: payParams.nonceStr || payParams.nonce_str,
      package: payParams.packageValue || payParams.package,
      signType: payParams.signType || 'RSA',
      paySign: payParams.paySign || payParams.sign
    }
  }

  // 验证必要参数
  const requiredParams = ['timeStamp', 'nonceStr', 'package', 'signType', 'paySign']
  for (const param of requiredParams) {
    if (!normalizedParams[param]) {
      throw new Error(`微信支付缺少必要参数: ${param}，请检查后端返回数据`)
    }
  }

  console.log('标准化后的支付参数:', normalizedParams)
  return normalizedParams
}

/**
 * 微信支付错误处理和调试信息
 * @param {Object} error - 错误对象
 * @param {String} orderNo - 订单号
 * @returns {Object} - 格式化的错误信息
 */
function handleWxPayError(error, orderNo) {
  console.error('微信支付错误详情:', error)

  // 常见错误码处理
  const errorMap = {
    'requestPayment:fail cancel': '用户取消支付',
    'requestPayment:fail': '支付失败',
    'requestPayment:fail invalid_request': '请求参数错误',
    'requestPayment:fail system_error': '系统错误，请稍后重试',
    'requestPayment:fail user_cancel': '用户取消支付',
    'requestPayment:fail payment_timeout': '支付超时',
    'requestPayment:fail insufficient_balance': '余额不足'
  }

  const errorMsg = error.errMsg || error.message || '未知错误'
  const friendlyMsg = errorMap[errorMsg] || errorMsg

  // 判断是否为用户取消
  const isCanceled = errorMsg.includes('cancel') || errorMsg.includes('Cancel')

  return {
    success: false,
    orderNo: orderNo,
    message: friendlyMsg,
    canceled: isCanceled,
    originalError: errorMsg,
    errorCode: error.errCode || error.code
  }
}

/**
 * 检查支付状态并处理结果
 * @param {String} orderNo - 订单号
 * @returns {Promise} - 支付状态检查结果
 */
async function checkPaymentStatus(orderNo) {
  try {
    const result = await queryPayStatus(orderNo)
    console.log('支付状态查询结果:', result)

    if (result.code === 200) {
      const payStatus = result.data.status
      const statusMap = {
        'PAID': '支付成功',
        'UNPAID': '未支付',
        'CANCELLED': '已取消',
        'REFUNDED': '已退款',
        'FAILED': '支付失败'
      }

      return {
        success: true,
        status: payStatus,
        statusText: statusMap[payStatus] || '未知状态',
        data: result.data
      }
    } else {
      throw new Error(result.msg || '查询支付状态失败')
    }
  } catch (error) {
    console.error('查询支付状态失败:', error)
    return {
      success: false,
      message: error.message || '网络请求失败'
    }
  }
}

/**
 * 微信支付重试机制
 * @param {String} orderNo - 订单号
 * @param {Number} maxRetries - 最大重试次数
 * @returns {Promise} - 重试结果
 */
async function retryWxPayment(orderNo, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      console.log(`微信支付重试第 ${i + 1} 次`)
      const result = await requestWxPayment(orderNo)
      return result
    } catch (error) {
      console.error(`第 ${i + 1} 次重试失败:`, error)

      // 如果是用户取消，不进行重试
      if (error.canceled) {
        throw error
      }

      // 最后一次重试失败
      if (i === maxRetries - 1) {
        throw error
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}

/**
 * 创建订单的统一入口
 * @param {String} orderType - 订单类型 (course, meditation, consultant, assessment)
 * @param {Object} orderData - 订单数据
 * @returns {Promise} - 创建结果Promise
 */
export async function createOrder(orderType, orderData) {
  try {
    let result

    switch (orderType) {
      case ORDER_TYPES.COURSE:
        result = await createCourseOrder(orderData)
        break
      case ORDER_TYPES.MEDITATION:
        result = await createMeditationOrder(orderData)
        break
      case ORDER_TYPES.CONSULTANT:
        result = await createConsultantOrder(orderData)
        break
      case ORDER_TYPES.ASSESSMENT:
        result = await createAssessmentOrder(orderData)
        break
      default:
        throw new Error('不支持的订单类型')
    }

    if (result.code === 200) {
      return {
        success: true,
        data: result.data,
        message: result.msg || '创建订单成功'
      }
    } else {
      throw new Error(result.msg || '创建订单失败')
    }
  } catch (error) {
    console.error('创建订单失败:', error)
    return {
      success: false,
      message: error.message || '创建订单失败'
    }
  }
}

/**
 * 发起微信支付
 * @param {String} orderNo - 订单号
 * @returns {Promise} - 支付结果Promise
 */
export function requestWxPayment(orderNo) {
  return new Promise(async (resolve, reject) => {
    try {
      // 调用支付API获取支付参数
      const result = await payOrder(orderNo)

      if (result.code !== 200) {
        throw new Error(result.msg || '获取支付参数失败')
      }

      const payParams = result.data.payParams

      // 使用统一的参数处理函数
      const paymentParams = normalizeWxPayParams(payParams)

      // 调用微信支付
      uni.requestPayment({
        ...paymentParams,
        success: (payRes) => {
          console.log('支付成功', payRes)
          resolve({
            success: true,
            orderNo: orderNo,
            transactionId: payRes.transactionId,
            message: '支付成功'
          })
        },
        fail: (payErr) => {
          const errorResult = handleWxPayError(payErr, orderNo)
          reject(errorResult)
        }
      })
    } catch (error) {
      console.error('请求支付参数失败', error)
      reject({
        success: false,
        orderNo: orderNo,
        message: error.message || '网络请求失败',
        canceled: false
      })
    }
  })
}

/**
 * 查询支付状态
 * @param {String} orderNo - 订单号
 * @returns {Promise} - 查询结果Promise
 */
export async function queryPaymentStatus(orderNo) {
  try {
    const result = await queryPayStatus(orderNo)

    if (result.code === 200) {
      const payData = result.data
      return {
        success: true,
        paid: payData.tradeState === 'SUCCESS',
        orderNo: orderNo,
        tradeState: payData.tradeState,
        tradeStateDesc: payData.tradeStateDesc,
        transactionId: payData.transactionId,
        successTime: payData.successTime,
        message: payData.tradeStateDesc || '查询成功'
      }
    } else {
      throw new Error(result.msg || '查询支付状态失败')
    }
  } catch (error) {
    console.error('查询支付状态失败:', error)
    return {
      success: false,
      orderNo: orderNo,
      message: error.message || '查询支付状态失败'
    }
  }
}

/**
 * 取消订单
 * @param {String} orderNo - 订单号
 * @returns {Promise} - 取消结果Promise
 */
export async function cancelOrder(orderNo) {
  try {
    const result = await cancelOrderAPI(orderNo)

    if (result.code === 200) {
      return {
        success: true,
        orderNo: orderNo,
        message: result.msg || '取消订单成功'
      }
    } else {
      throw new Error(result.msg || '取消订单失败')
    }
  } catch (error) {
    console.error('取消订单失败:', error)
    return {
      success: false,
      orderNo: orderNo,
      message: error.message || '取消订单失败'
    }
  }
}

/**
 * 申请退款
 * @param {String} orderNo - 订单号
 * @param {Number} refundAmount - 退款金额
 * @param {String} reason - 退款原因
 * @returns {Promise} - 退款结果Promise
 */
export async function requestRefund(orderNo, refundAmount, reason) {
  try {
    const result = await requestRefundAPI({
      orderNo: orderNo,
      refundAmount: refundAmount,
      reason: reason || '用户主动申请退款'
    })

    if (result.code === 200) {
      return {
        success: true,
        data: result.data,
        message: result.msg || '申请退款成功'
      }
    } else {
      throw new Error(result.msg || '申请退款失败')
    }
  } catch (error) {
    console.error('申请退款失败:', error)
    return {
      success: false,
      message: error.message || '申请退款失败'
    }
  }
}

// /**
//  * 完整的购买流程
//  * @param {String} orderType - 订单类型 (course, meditation, consultant, assessment)
//  * @param {Object} productData - 商品数据
//  * @param {Object} orderData - 订单数据
//  * @returns {Promise} - 购买结果Promise
//  */
// export async function buyProduct(orderType, productData, orderData) {
//   try {
//     // 1. 创建订单
//     const createResult = await createOrder(orderType, orderData)
//     if (!createResult.success) {
//       throw new Error(createResult.message)
//     }

//     const orderInfo = createResult.data
//     const orderNo = orderInfo.orderNo

//     // 2. 发起支付
//     const payResult = await requestWxPayment(orderNo)
//     if (!payResult.success) {
//       throw payResult
//     }

//     // 3. 返回成功结果
//     return {
//       success: true,
//       orderNo: orderNo,
//       orderId: orderInfo.orderId,
//       transactionId: payResult.transactionId,
//       productData: productData,
//       message: '购买成功'
//     }

//   } catch (error) {
//     console.error('购买失败:', error)
//     throw error
//   }
// }

/**
 * 构建订单数据
 * @param {String} orderType - 订单类型
 * @param {Object} productInfo - 商品信息
 * @param {Object} options - 选项 (优惠券、积分等)
 * @returns {Object} 订单数据
 */
export function buildOrderData(orderType, productInfo, options = {}) {
  const baseData = {
    productId: productInfo.id,
    paymentAmount: options.paymentAmount || productInfo.price,
    originalPrice: productInfo.originalPrice || productInfo.price
  }

  // 添加优惠券信息
  if (options.couponId) {
    baseData.couponId = options.couponId
    baseData.couponDiscount = options.couponDiscount || 0
  }

  // 添加积分信息
  if (options.pointsUsed) {
    baseData.pointsUsed = options.pointsUsed
    baseData.pointsDiscount = options.pointsDiscount || 0
  }

  // 咨询订单特殊字段
  if (orderType === ORDER_TYPES.CONSULTANT) {
    baseData.consultantId = productInfo.consultantId || productInfo.id
    baseData.serviceId = productInfo.serviceId
    baseData.appointmentTime = options.appointmentTime
    baseData.duration = options.duration || 50
  }

  return baseData
}

/**
 * 完整的购买流程
 * @param {String} orderType - 订单类型 (course, meditation, consultant, assessment)
 * @param {Object} productData - 商品数据
 * @param {Object} orderOptions - 订单选项
 * @returns {Promise} - 购买结果Promise
 */
export async function buyProduct(orderType, productData, orderOptions = {}) {
  try {
    // 1. 创建订单
    const orderData = buildOrderData(orderType, productData, orderOptions)
    const createResult = await createOrder(orderType, orderData)

    if (!createResult.success) {
      throw new Error(createResult.message)
    }

    const orderInfo = createResult.data
    const orderNo = orderInfo.orderNo

    // 2. 发起支付
    const payResult = await requestWxPayment(orderNo)
    if (!payResult.success) {
      throw payResult
    }

    // 3. 返回成功结果
    return {
      success: true,
      orderNo: orderNo,
      orderId: orderInfo.orderId,
      transactionId: payResult.transactionId,
      productData: productData,
      message: '购买成功'
    }

  } catch (error) {
    console.error('购买失败:', error)
    throw error
  }
}

/**
 * 显示支付结果
 * @param {Object} result - 支付结果
 * @param {Boolean} result.success - 是否成功
 * @param {String} result.message - 消息
 * @param {Boolean} result.canceled - 是否用户取消
 */
export function showPaymentResult(result) {
  if (result.success) {
    uni.showToast({
      title: result.message || '支付成功',
      icon: 'success',
      duration: 2000
    })
  } else {
    // 用户取消支付不显示错误提示
    if (!result.canceled) {
      uni.showToast({
        title: result.message || '支付失败',
        icon: 'none',
        duration: 2000
      })
    }
  }
}

// /**
//  * 构建订单数据
//  * @param {String} orderType - 订单类型
//  * @param {Object} productInfo - 商品信息
//  * @param {Object} options - 选项 (优惠券、积分等)
//  * @returns {Object} 订单数据
//  */
// export function buildOrderData(orderType, productInfo, options = {}) {
//   const baseData = {
//     productId: productInfo.id,
//     paymentAmount: options.paymentAmount || productInfo.price,
//     originalPrice: productInfo.originalPrice || productInfo.price
//   }

//   // 添加优惠券信息
//   if (options.couponId) {
//     baseData.couponId = options.couponId
//     baseData.couponDiscount = options.couponDiscount || 0
//   }

//   // 添加积分信息
//   if (options.pointsUsed) {
//     baseData.pointsUsed = options.pointsUsed
//     baseData.pointsDiscount = options.pointsDiscount || 0
//   }

//   return baseData
// }

/**
 * 获取订单类型前缀
 * @param {String} orderType - 订单类型
 * @returns {String} 订单前缀
 */
export function getOrderPrefix(orderType) {
  const prefixMap = {
    'course': 'COURSE_',
    'meditation': 'MEDITATION_',
    'consultant': 'CONSULTANT_',
    'assessment': 'ASSESSMENT_'
  }
  return prefixMap[orderType] || 'ORDER_'
}

// 导出新增的工具函数
export {
  normalizeWxPayParams,
  handleWxPayError,
  checkPaymentStatus,
  retryWxPayment
}
