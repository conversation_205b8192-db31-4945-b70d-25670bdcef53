<template>
	<view class="coupon-center">

		<!-- 标签页 -->
		<!-- <view class="tabs">
			<view class="tab-item" :class="{ 'active': activeTab === 'unused' }" @click="switchTab('unused')">
				<text class="tab-text">未使用</text>
			</view>
			<view class="tab-item" :class="{ 'active': activeTab === 'used' }" @click="switchTab('used')">
				<text class="tab-text">已使用</text>
			</view>
			<view class="tab-item" :class="{ 'active': activeTab === 'expired' }" @click="switchTab('expired')">
				<text class="tab-text">已过期</text>
			</view>
		</view> -->
		<view class="result-category-tabs">
			<view v-for="(tab, index) in tabList" :key="tab.key" :class="['tab-item', { active: activeTab === tab.key }]"
				@click="switchTab(tab.key)">
				{{ tab.name }}
				<image v-if="activeTab === tab.key" class="tab-icon" src="../../../static/icon/形状 6-active.png"></image>
			</view>
		</view>

		<!-- 优惠券列表 -->
		<scroll-view class="coupon-list" scroll-y="true" show-scrollbar="false">
			<view class="coupon-item" v-for="(coupon, index) in filteredCoupons" :key="index">
				<view class="coupon-left">
					<view class="coupon-amount">
						<text class="amount-number">{{ coupon.amount }}</text>
						<text class="amount-unit">{{ coupon.unit }}</text>
					</view>
				</view>

				<view class="coupon-right">
					<image class="status" src="../../static/icon/形状 <EMAIL>"></image>
					<view class="coupon-info">
						<text class="coupon-title">{{ coupon.title }}</text>
						<text class="coupon-desc">{{ coupon.description }}</text>
						<text class="coupon-expire">有效期至{{ coupon.expireDate }}</text>
					</view>

					<view class="coupon-action">
						<button class="action-btn" :class="{
							'received': coupon.status === 'used',
							'expired': coupon.status === 'expired'
						}" @click="handleCouponAction(coupon)" :disabled="coupon.status !== 'unused'">
							{{ getCouponActionText(coupon.status) }}
						</button>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="filteredCoupons.length === 0">
				<uni-icons type="gift" size="60" color="#ccc"></uni-icons>
				<text class="empty-text">暂无{{ getEmptyText() }}优惠券</text>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 当前激活的标签页
const activeTab = ref('unused');

const tabList = [
	{ key: 'unused', name: '未使用' },
	{ key: 'used', name: '已使用' },
	{ key: 'expired', name: '已过期' }
]

// 优惠券数据
const coupons = ref([
	{
		id: 1,
		title: '咨询优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'unused'
	},
	{
		id: 2,
		title: '测评优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'unused'
	},
	{
		id: 3,
		title: '冥想优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'unused'
	},
	{
		id: 4,
		title: '课程优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'unused'
	},
	{
		id: 5,
		title: '咨询优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'used'
	},
	{
		id: 6,
		title: '测评优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '15',
		unit: '优惠券(元)',
		expireDate: '2024/12/12',
		status: 'expired'
	}
]);

// 过滤后的优惠券列表
const filteredCoupons = computed(() => {
	return coupons.value.filter(coupon => coupon.status === activeTab.value);
});

// 切换标签页
const switchTab = (tab) => {
	activeTab.value = tab;
};

// 返回上一页
const goBack = () => {
	uni.navigateBack();
};

// 获取优惠券操作按钮文本
const getCouponActionText = (status) => {
	switch (status) {
		case 'unused':
			return '待领取';
		case 'used':
			return '已领取';
		case 'expired':
			return '已过期';
		default:
			return '待领取';
	}
};

// 获取空状态文本
const getEmptyText = () => {
	switch (activeTab.value) {
		case 'unused':
			return '未使用的';
		case 'used':
			return '已使用的';
		case 'expired':
			return '已过期的';
		default:
			return '';
	}
};

// 处理优惠券操作
const handleCouponAction = (coupon) => {
	if (coupon.status === 'unused') {
		// 领取优惠券
		uni.showModal({
			title: '领取优惠券',
			content: `确认领取${coupon.title}吗？`,
			success: (res) => {
				if (res.confirm) {
					// 更新优惠券状态
					coupon.status = 'used';
					uni.showToast({
						title: '领取成功',
						icon: 'success'
					});
				}
			}
		});
	}
};

// 页面加载
onMounted(() => {
	// 可以在这里加载优惠券数据
	console.log('领券中心页面加载完成');
});
</script>

<style lang="scss" scoped>
.coupon-center {
	min-height: 100vh;
	background: #F5F5F5;

	// 顶部导航栏
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 32rpx;
		padding-top: calc(var(--status-bar-height) + 20rpx);
		background: #fff;

		.nav-left {
			width: 60rpx;
		}

		.nav-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
		}

		.nav-right {
			display: flex;
			align-items: center;
			width: 60rpx;
			justify-content: flex-end;
		}
	}

	// 搜索结果分类标签样式
	.result-category-tabs {
		display: flex;
		background: #fff;
		padding: 32rpx;
		padding-top: 47rpx;
		padding-bottom: 28rpx;
		justify-content: space-between;

		// border-bottom: 1px solid #eee;
		.tab-item {
			position: relative;
			// padding: 24rpx 32rpx;
			font-size: 28rpx;
			color: #8A8788;
			position: relative;
			margin-right: 148rpx;

			&.active {
				font-size: 32rpx;
				color: #A04571;
				display: flex;
				flex-direction: column;
				align-items: center;

				.tab-icon {
					width: 28rpx;
					height: 12rpx;
					margin-top: 10rpx;
				}
			}
		}

		.tab-item:last-child {
			margin-right: 0;
		}

		.tab-icon {
			width: 26rpx;
			height: 26rpx;
		}
	}

	// 优惠券列表
	.coupon-list {
		height: calc(100vh - 200rpx);
		width: calc(100% - 64rpx);
		padding: 20rpx 32rpx;
		position: relative;

		.coupon-item {
			display: flex;
			background: #fff;
			border-radius: 8rpx;
			margin-bottom: 16rpx;
			overflow: hidden;

			.coupon-left {
				width: 183rpx;
				// background: linear-gradient(135deg, #A04571 0%, #D4749A 100%);
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;

				// // 右侧锯齿边
				// &::after {
				// 	content: '';
				// 	position: absolute;
				// 	right: -10rpx;
				// 	top: 0;
				// 	bottom: 0;
				// 	width: 20rpx;
				// 	background: radial-gradient(circle at 0 50%, transparent 10rpx, #fff 10rpx);
				// 	background-size: 20rpx 20rpx;
				// }

				.coupon-amount {
					text-align: center;

					.amount-number {
						display: block;
						font-size: 48rpx;
						font-weight: bold;
						color: #A04571;
						line-height: 1;
					}

					.amount-unit {
						font-size: 24rpx;
						color: #A04571;
					}
				}
			}

			.coupon-right {
				flex: 1;
				display: flex;
				padding: 32rpx 24rpx;

				.status {
					position: absolute;
					right: 0;
					top: 0;
					width: 126rpx;
					height: 126rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}

				.coupon-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.coupon-title {
						font-size: 34rpx;
						font-weight: 500;
						color: #000;
						margin-bottom: 8rpx;
					}

					.coupon-desc {
						font-size: 20rpx;
						color: #8A8788;
						margin-bottom: 8rpx;
					}

					.coupon-expire {
						font-size: 24rpx;
						color: #ACA8AA;
					}
				}

				.coupon-action {
					display: flex;
					align-items: center;

					.action-btn {
						background: #A04571;
						color: #fff;
						border-radius: 8rpx;
						font-size: 24rpx;
						border: none;
						min-width: 120rpx;

						&.received {
							background: #E5E5E5;
							color: #999;
						}

						&.expired {
							background: #F5F5F5;
							color: #ccc;
							border: 1rpx solid #E5E5E5;
						}

						&:not(:disabled):active {
							opacity: 0.8;
						}
					}
				}
			}
		}

		// 空状态
		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 120rpx 0;

			.empty-text {
				font-size: 28rpx;
				color: #999;
				margin-top: 24rpx;
			}
		}
	}
}
</style>