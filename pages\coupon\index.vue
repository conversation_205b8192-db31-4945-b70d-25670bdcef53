<template>
	<view class="coupon-center">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<view class="nav-title">领券中心</view>
			<view class="nav-right">
				<uni-icons type="more-filled" size="20" color="#333"></uni-icons>
				<uni-icons type="help" size="20" color="#333" style="margin-left: 16rpx;"></uni-icons>
			</view>
		</view>

		<!-- 标签页 -->
		<view class="tabs">
			<view class="tab-item" :class="{ 'active': activeTab === 'unused' }" @click="switchTab('unused')">
				<text class="tab-text">未使用</text>
			</view>
			<view class="tab-item" :class="{ 'active': activeTab === 'used' }" @click="switchTab('used')">
				<text class="tab-text">已使用</text>
			</view>
			<view class="tab-item" :class="{ 'active': activeTab === 'expired' }" @click="switchTab('expired')">
				<text class="tab-text">已过期</text>
			</view>
		</view>

		<!-- 优惠券列表 -->
		<scroll-view class="coupon-list" scroll-y="true" show-scrollbar="false">
			<view class="coupon-item" v-for="(coupon, index) in filteredCoupons" :key="index">
				<view class="coupon-left">
					<view class="coupon-amount">
						<text class="amount-number">{{ coupon.amount }}</text>
						<text class="amount-unit">{{ coupon.unit }}</text>
					</view>
				</view>

				<view class="coupon-right">
					<view class="coupon-info">
						<text class="coupon-title">{{ coupon.title }}</text>
						<text class="coupon-desc">{{ coupon.description }}</text>
						<text class="coupon-expire">有效期至{{ coupon.expireDate }}</text>
					</view>

					<view class="coupon-action">
						<button class="action-btn" :class="{
							'received': coupon.status === 'used',
							'expired': coupon.status === 'expired'
						}" @click="handleCouponAction(coupon)" :disabled="coupon.status !== 'unused'">
							{{ getCouponActionText(coupon.status) }}
						</button>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="filteredCoupons.length === 0">
				<uni-icons type="gift" size="60" color="#ccc"></uni-icons>
				<text class="empty-text">暂无{{ getEmptyText() }}优惠券</text>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 当前激活的标签页
const activeTab = ref('unused');

// 优惠券数据
const coupons = ref([
	{
		id: 1,
		title: '咨询优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'unused'
	},
	{
		id: 2,
		title: '测评优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'unused'
	},
	{
		id: 3,
		title: '冥想优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'unused'
	},
	{
		id: 4,
		title: '课程优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'unused'
	},
	{
		id: 5,
		title: '咨询优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '20',
		unit: '优惠券(元)',
		expireDate: '2025/12/12',
		status: 'used'
	},
	{
		id: 6,
		title: '测评优惠券',
		description: '每个订单享可使用一张优惠券',
		amount: '15',
		unit: '优惠券(元)',
		expireDate: '2024/12/12',
		status: 'expired'
	}
]);

// 过滤后的优惠券列表
const filteredCoupons = computed(() => {
	return coupons.value.filter(coupon => coupon.status === activeTab.value);
});

// 切换标签页
const switchTab = (tab) => {
	activeTab.value = tab;
};

// 返回上一页
const goBack = () => {
	uni.navigateBack();
};

// 获取优惠券操作按钮文本
const getCouponActionText = (status) => {
	switch (status) {
		case 'unused':
			return '待领取';
		case 'used':
			return '已领取';
		case 'expired':
			return '已过期';
		default:
			return '待领取';
	}
};

// 获取空状态文本
const getEmptyText = () => {
	switch (activeTab.value) {
		case 'unused':
			return '未使用的';
		case 'used':
			return '已使用的';
		case 'expired':
			return '已过期的';
		default:
			return '';
	}
};

// 处理优惠券操作
const handleCouponAction = (coupon) => {
	if (coupon.status === 'unused') {
		// 领取优惠券
		uni.showModal({
			title: '领取优惠券',
			content: `确认领取${coupon.title}吗？`,
			success: (res) => {
				if (res.confirm) {
					// 更新优惠券状态
					coupon.status = 'used';
					uni.showToast({
						title: '领取成功',
						icon: 'success'
					});
				}
			}
		});
	}
};

// 页面加载
onMounted(() => {
	// 可以在这里加载优惠券数据
	console.log('领券中心页面加载完成');
});
</script>

<style lang="scss" scoped>
.coupon-center {
	min-height: 100vh;
	background: #F5F5F5;

	// 顶部导航栏
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 32rpx;
		padding-top: calc(var(--status-bar-height) + 20rpx);
		background: #fff;

		.nav-left {
			width: 60rpx;
		}

		.nav-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
		}

		.nav-right {
			display: flex;
			align-items: center;
			width: 60rpx;
			justify-content: flex-end;
		}
	}

	// 标签页
	.tabs {
		display: flex;
		background: #fff;
		border-bottom: 1rpx solid #E5E5E5;

		.tab-item {
			flex: 1;
			padding: 32rpx 0;
			text-align: center;
			position: relative;

			&.active {
				.tab-text {
					color: #A04571;
					font-weight: 600;
				}

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 60rpx;
					height: 4rpx;
					background: #A04571;
					border-radius: 2rpx;
				}
			}

			.tab-text {
				font-size: 28rpx;
				color: #666;
				transition: all 0.3s ease;
			}
		}
	}

	// 优惠券列表
	.coupon-list {
		height: calc(100vh - 200rpx);
		padding: 20rpx 32rpx;

		.coupon-item {
			display: flex;
			background: #fff;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			overflow: hidden;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

			.coupon-left {
				width: 160rpx;
				background: linear-gradient(135deg, #A04571 0%, #D4749A 100%);
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;

				// 右侧锯齿边
				&::after {
					content: '';
					position: absolute;
					right: -10rpx;
					top: 0;
					bottom: 0;
					width: 20rpx;
					background: radial-gradient(circle at 0 50%, transparent 10rpx, #fff 10rpx);
					background-size: 20rpx 20rpx;
				}

				.coupon-amount {
					text-align: center;

					.amount-number {
						display: block;
						font-size: 48rpx;
						font-weight: 700;
						color: #fff;
						line-height: 1;
					}

					.amount-unit {
						font-size: 20rpx;
						color: #fff;
						opacity: 0.9;
					}
				}
			}

			.coupon-right {
				flex: 1;
				display: flex;
				padding: 32rpx 24rpx;

				.coupon-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.coupon-title {
						font-size: 32rpx;
						font-weight: 600;
						color: #333;
						margin-bottom: 8rpx;
					}

					.coupon-desc {
						font-size: 24rpx;
						color: #666;
						margin-bottom: 8rpx;
					}

					.coupon-expire {
						font-size: 22rpx;
						color: #999;
					}
				}

				.coupon-action {
					display: flex;
					align-items: center;

					.action-btn {
						padding: 12rpx 24rpx;
						background: #A04571;
						color: #fff;
						border-radius: 20rpx;
						font-size: 24rpx;
						border: none;
						min-width: 120rpx;

						&.received {
							background: #E5E5E5;
							color: #999;
						}

						&.expired {
							background: #F5F5F5;
							color: #ccc;
							border: 1rpx solid #E5E5E5;
						}

						&:not(:disabled):active {
							opacity: 0.8;
						}
					}
				}
			}
		}

		// 空状态
		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 120rpx 0;

			.empty-text {
				font-size: 28rpx;
				color: #999;
				margin-top: 24rpx;
			}
		}
	}
}
</style>