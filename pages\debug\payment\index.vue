<template>
  <view class="payment-debug">
    <view class="header">
      <text class="title">微信支付调试工具</text>
      <text class="subtitle">用于诊断微信支付参数和版本问题</text>
    </view>

    <view class="section">
      <view class="section-title">支付测试</view>
      <view class="form-item">
        <text class="label">订单号:</text>
        <input 
          v-model="testOrderNo" 
          placeholder="输入测试订单号"
          class="input"
        />
      </view>
      <view class="form-item">
        <text class="label">金额(分):</text>
        <input 
          v-model.number="testAmount" 
          placeholder="输入测试金额(分)"
          type="number"
          class="input"
        />
      </view>
      <button @click="testPayment" class="test-btn" :disabled="testing">
        {{ testing ? '测试中...' : '测试微信支付' }}
      </button>
    </view>

    <view class="section">
      <view class="section-title">支付参数检查</view>
      <button @click="checkPayParams" class="check-btn">获取支付参数</button>
      <view v-if="payParams" class="params-display">
        <text class="params-title">原始参数:</text>
        <text class="params-content">{{ JSON.stringify(payParams, null, 2) }}</text>
      </view>
      <view v-if="normalizedParams" class="params-display">
        <text class="params-title">标准化参数:</text>
        <text class="params-content">{{ JSON.stringify(normalizedParams, null, 2) }}</text>
      </view>
      <view v-if="versionInfo" class="version-info">
        <text class="version-title">版本信息:</text>
        <text class="version-content">{{ versionInfo }}</text>
      </view>
    </view>

    <view class="section">
      <view class="section-title">支付状态查询</view>
      <view class="form-item">
        <text class="label">查询订单号:</text>
        <input 
          v-model="queryOrderNo" 
          placeholder="输入要查询的订单号"
          class="input"
        />
      </view>
      <button @click="queryStatus" class="query-btn">查询支付状态</button>
      <view v-if="statusResult" class="status-display">
        <text class="status-title">状态结果:</text>
        <text class="status-content">{{ JSON.stringify(statusResult, null, 2) }}</text>
      </view>
    </view>

    <view class="section">
      <view class="section-title">错误日志</view>
      <button @click="clearLogs" class="clear-btn">清空日志</button>
      <view class="logs-container">
        <view v-for="(log, index) in errorLogs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-content">{{ log.content }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  requestWxPayment, 
  normalizeWxPayParams, 
  checkPaymentStatus 
} from '@/utils/payment.js'
import { payOrder } from '@/api/payment.js'

// 响应式数据
const testOrderNo = ref('')
const testAmount = ref(100)
const testing = ref(false)
const payParams = ref(null)
const normalizedParams = ref(null)
const versionInfo = ref('')
const queryOrderNo = ref('')
const statusResult = ref(null)
const errorLogs = ref([])

// 添加日志
function addLog(content) {
  const now = new Date()
  const time = `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`
  errorLogs.value.unshift({
    time,
    content: typeof content === 'object' ? JSON.stringify(content) : content
  })
  
  // 限制日志数量
  if (errorLogs.value.length > 50) {
    errorLogs.value = errorLogs.value.slice(0, 50)
  }
}

// 测试支付
async function testPayment() {
  if (!testOrderNo.value) {
    uni.showToast({
      title: '请输入订单号',
      icon: 'none'
    })
    return
  }

  testing.value = true
  addLog(`开始测试支付，订单号: ${testOrderNo.value}`)

  try {
    const result = await requestWxPayment(testOrderNo.value)
    addLog(`支付测试成功: ${JSON.stringify(result)}`)
    
    uni.showToast({
      title: '支付测试成功',
      icon: 'success'
    })
  } catch (error) {
    addLog(`支付测试失败: ${JSON.stringify(error)}`)
    
    uni.showToast({
      title: error.message || '支付测试失败',
      icon: 'none'
    })
  } finally {
    testing.value = false
  }
}

// 检查支付参数
async function checkPayParams() {
  if (!testOrderNo.value) {
    uni.showToast({
      title: '请输入订单号',
      icon: 'none'
    })
    return
  }

  try {
    addLog(`开始获取支付参数，订单号: ${testOrderNo.value}`)
    
    const result = await payOrder(testOrderNo.value)
    if (result.code === 200) {
      payParams.value = result.data.payParams
      addLog(`获取支付参数成功: ${JSON.stringify(payParams.value)}`)
      
      // 尝试标准化参数
      try {
        normalizedParams.value = normalizeWxPayParams(payParams.value)
        addLog(`参数标准化成功: ${JSON.stringify(normalizedParams.value)}`)
        
        // 检测版本
        const isV2 = payParams.value.total_fee || 
                     payParams.value.appId || 
                     (payParams.value.signType && payParams.value.signType.toUpperCase() === 'MD5')
        versionInfo.value = isV2 ? '微信支付 V2 版本' : '微信支付 V3 版本'
        addLog(`检测到版本: ${versionInfo.value}`)
        
      } catch (error) {
        addLog(`参数标准化失败: ${error.message}`)
        normalizedParams.value = null
        versionInfo.value = '版本检测失败'
      }
    } else {
      throw new Error(result.msg || '获取支付参数失败')
    }
  } catch (error) {
    addLog(`获取支付参数失败: ${error.message}`)
    payParams.value = null
    normalizedParams.value = null
    versionInfo.value = ''
    
    uni.showToast({
      title: error.message || '获取参数失败',
      icon: 'none'
    })
  }
}

// 查询支付状态
async function queryStatus() {
  if (!queryOrderNo.value) {
    uni.showToast({
      title: '请输入查询订单号',
      icon: 'none'
    })
    return
  }

  try {
    addLog(`开始查询支付状态，订单号: ${queryOrderNo.value}`)
    
    const result = await checkPaymentStatus(queryOrderNo.value)
    statusResult.value = result
    addLog(`查询支付状态完成: ${JSON.stringify(result)}`)
    
    uni.showToast({
      title: result.success ? '查询成功' : '查询失败',
      icon: result.success ? 'success' : 'none'
    })
  } catch (error) {
    addLog(`查询支付状态失败: ${error.message}`)
    statusResult.value = null
    
    uni.showToast({
      title: error.message || '查询失败',
      icon: 'none'
    })
  }
}

// 清空日志
function clearLogs() {
  errorLogs.value = []
  addLog('日志已清空')
}

// 页面加载时初始化
onMounted(() => {
  // 生成测试订单号
  testOrderNo.value = `TEST_${Date.now()}`
  addLog('微信支付调试工具已加载')
})
</script>

<style lang="scss" scoped>
.payment-debug {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  border-left: 6rpx solid #007aff;
  padding-left: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  
  .label {
    width: 160rpx;
    font-size: 28rpx;
    color: #333;
  }
  
  .input {
    flex: 1;
    height: 80rpx;
    padding: 0 20rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}

.test-btn, .check-btn, .query-btn, .clear-btn {
  width: 100%;
  height: 80rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.test-btn:disabled {
  background-color: #ccc;
}

.params-display, .status-display {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.params-title, .status-title, .version-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.params-content, .status-content, .version-content {
  display: block;
  font-size: 24rpx;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
}

.version-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #e8f4fd;
  border-radius: 8rpx;
}

.logs-container {
  max-height: 600rpx;
  overflow-y: auto;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
}

.log-item {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #e0e0e0;
  
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.log-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.log-content {
  display: block;
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}
</style>
