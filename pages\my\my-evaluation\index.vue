<template>
  <view class="my-evaluation">
    <!-- 分类标签 -->
    <!-- <view class="category-tabs">
      <view class="tab-item" v-for="tab in tabList" :key="tab.key" :class="{ active: currentTab === tab.key }"
        @click="switchTab(tab.key)">
        <text>{{ tab.name }}</text>
      </view>
    </view> -->
    <view class="result-category-tabs">
      <view v-for="(tab, index) in tabList" :key="tab.key" :class="['tab-item', { active: currentTab === tab.key }]"
        @click="switchTab(tab.key)">
        {{ tab.name }}
        <image v-if="currentTab === tab.key" class="tab-icon" src="../../../static/icon/形状 6-active.png"></image>
      </view>
    </view>

    <!-- 测评列表 -->
    <scroll-view class="evaluation-list" scroll-y>
      <!-- 空状态 -->
      <view class="empty" v-if="currentList.length === 0">
        <text>暂无测评记录</text>
      </view>

      <!-- 使用 OrderListItem 组件 -->
      <view v-else class="list-container">
        <OrderListItem v-for="item in currentList" :key="item.id" :item="item" :type="'assessment'"
          @click="handleItemClick" @action="handleAction" />
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from "@dcloudio/uni-app";
import { getAssessmentRecords } from '@/api/evaluation'
import { formatTime } from '@/utils/index'
import OrderListItem from '@/components/OrderListItem/OrderListItem.vue'

const currentTab = ref('all')
const evaluationList = ref([])
const loading = ref(false)

// 分类标签配置
const tabList = [
  { key: 'all', name: '全部' },
  { key: 'pending', name: '待完成' },
  { key: 'completed', name: '已完成' }
]

// 计算当前分类下的列表
const currentList = computed(() => {
  // 将后端数据转换为 OrderListItem 组件期望的格式
  const transformedList = evaluationList.value.map(item => ({
    id: item.id,
    assessmentName: item.scaleName || '测评',
    assessmentCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png',
    status: getOrderStatus(item.status, item), // 转换状态格式，传入item用于判断
    estimatedTime: 15, // 默认预估时间，可以根据实际情况调整
    createTime: formatTime(item.createTime),
    actualPrice: '0', // 测评价格，可以根据实际情况调整
    type: 'assessment',
    // 保留原始数据以便其他操作使用
    originalData: item,
    scaleId: item.scaleId,
    totalScore: item.totalScore,
    statusDesc: item.statusDesc
  }))

  // 根据选择的分类筛选
  if (currentTab.value === 'all') {
    return transformedList
  }
  return transformedList.filter(item => {
    if (currentTab.value === 'pending') {
      return item.originalData.status === 0 // 进行中
    }
    if (currentTab.value === 'completed') {
      return item.originalData.status === 1 // 已完成
    }
    return true
  })
})

// 将后端状态转换为 OrderListItem 组件期望的状态格式
const getOrderStatus = (backendStatus, item) => {
  switch (backendStatus) {
    case 0:
      return 'in_progress' // 进行中
    case 1:
      return 'completed' // 已完成
    default:
      // 如果没有开始时间，说明是待使用状态
      if (!item.startTime) {
        return 'pending' // 待使用
      }
      return 'in_progress' // 默认进行中
  }
}

onLoad(() => {
  getList()
})

// 切换分类
const switchTab = (tabKey) => {
  currentTab.value = tabKey
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '进行中',
    1: '暂停中',
    2: '已完成'
  }
  return statusMap[status] || '未知'
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    0: 'in_progress',
    1: 'paused',
    2: 'completed'
  }
  return classMap[status] || 'pending'
}

// 获取状态图标
const getStatusIcon = (status) => {
  const iconMap = {
    0: '⏳',
    1: '⏸️',
    2: '✅'
  }
  return iconMap[status] || '❓'
}

// 格式化时间
// const formatTime = (timeStr) => {
//   if (!timeStr) return ''
//   const date = new Date(timeStr)
//   return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
// }

// 获取列表数据
const getList = async () => {
  if (loading.value) return
  loading.value = true
  try {
    const res = await getAssessmentRecords()
    if (res.code === 200) {
      evaluationList.value = res.data || []
    } else {
      uni.showToast({
        title: res.msg || '获取列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取测评记录失败:', error)
    uni.showToast({
      title: '获取列表失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 查看详情
const viewDetail = (scaleId) => {
  // 查找对应的测评记录
  const record = evaluationList.value.find(record => record.scaleId === scaleId)
  if (record) {
    // 如果有记录，传递recordId和status
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${scaleId}&recordId=${record.id}&status=${record.status}`
    })
  } else {
    // 没有记录，正常跳转
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${scaleId}`
    })
  }
}

// 处理 OrderListItem 组件的点击事件
const handleItemClick = (item) => {
  console.log('点击测评项:', item)
  // 使用原始数据进行跳转
  const originalData = item.originalData
  if (originalData && originalData.scaleId) {
    viewDetail(originalData.scaleId)
  } else {
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${item.scaleId || item.id}`
    })
  }
}

// 处理 OrderListItem 组件的操作事件
const handleAction = ({ action, item }) => {
  console.log('处理操作:', action, item)
  const originalData = item.originalData

  switch (action) {
    case 'detail':
      handleItemClick(item)
      break
    case 'continue':
      // 继续测评
      uni.navigateTo({
        url: `/pages/evaluation/test/index?id=${originalData?.scaleId || item.scaleId || item.id}`
      })
      break
    case 'start':
      // 开始测评（去测评）
      uni.navigateTo({
        url: `/pages/evaluation/test/index?id=${originalData?.scaleId || item.scaleId || item.id}`
      })
      break
    case 'view_result':
      // 查看结果
      if (originalData?.reportGenerated === 1) {
        // 有报告，跳转到报告页面
        uni.navigateTo({
          url: `/pages/evaluation/report/index?id=${originalData.id}`
        })
      } else {
        // 没有报告，跳转到结果页面
        uni.navigateTo({
          url: `/pages/evaluation/result/index?id=${originalData?.scaleId || item.scaleId || item.id}&recordId=${originalData?.id}`
        })
      }
      break
    default:
      handleItemClick(item)
      break
  }
}
</script>

<style lang="scss" scoped>
.my-evaluation {
  min-height: 100vh;
  background-color: #f5f5f5;

  // 搜索结果分类标签样式
  .result-category-tabs {
    display: flex;
    background: #fff;
    padding: 32rpx;
    padding-top: 47rpx;
    padding-bottom: 28rpx;
    justify-content: space-between;

    // border-bottom: 1px solid #eee;
    .tab-item {
      position: relative;
      // padding: 24rpx 32rpx;
      font-size: 28rpx;
      color: #8A8788;
      position: relative;
      margin-right: 148rpx;

      &.active {
        font-size: 32rpx;
        color: #A04571;
        display: flex;
        flex-direction: column;
        align-items: center;

        .tab-icon {
          width: 28rpx;
          height: 12rpx;
          margin-top: 10rpx;
        }
      }
    }

    .tab-item:last-child {
      margin-right: 0;
    }

    .tab-icon {
      width: 26rpx;
      height: 26rpx;
    }
  }
}

.my-evaluation-header {
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;

  .my-evaluation-header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
  }
}

.category-tabs {
  display: flex;
  background-color: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #eee;

  .tab-item {
    flex: 1;
    padding: 30rpx 0;
    text-align: center;
    font-size: 28rpx;
    color: #666;
    position: relative;

    &.active {
      color: #409eff;
      font-weight: bold;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #409eff;
        border-radius: 2rpx;
      }
    }
  }
}

.evaluation-list {
  width: calc(100vw - 64rpx);
  height: calc(100vh - 186rpx);
  padding: 24rpx 32rpx;

  .list-container {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  .list-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

    .item-image {
      width: 100%;
      height: 300rpx;
      border-radius: 8rpx;
      margin-bottom: 20rpx;
    }

    .item-main {
      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      .desc {
        font-size: 28rpx;
        color: #666;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }

    .item-footer {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .footer-left {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 15rpx;

        .status-icon {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            font-size: 20rpx;
          }

          &.pending {
            background-color: #fff7e6;
            color: #ff9500;
          }

          &.in_progress {
            background-color: #f0f9ff;
            color: #409eff;
          }

          &.paused {
            background-color: #fef0f0;
            color: #f56c6c;
          }

          &.completed {
            background-color: #f0f9ff;
            color: #67c23a;
          }
        }

        .info-group {
          display: flex;
          align-items: center;
          gap: 15rpx;

          .question-count {
            font-size: 22rpx;
            color: #666;
            background-color: #f5f5f5;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
          }

          .score {
            font-size: 28rpx;
            color: #fff;
            font-weight: bold;
            background: linear-gradient(135deg, #409eff, #67c23a);
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.3);
          }
        }
      }

      .footer-right {
        display: flex;
        align-items: center;
        gap: 15rpx;

        .time {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }

  // .empty {
  //   text-align: center;
  //   padding: 100rpx 0;
  //   color: #999;
  // }
}
</style>