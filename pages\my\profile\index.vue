<template>
	<view class="profile-container">
		<!-- 头像设置 -->
		<!-- <view class="avatar-section">
			<view class="avatar-item" @click="changeAvatar">
				<image class="avatar" :src="profileForm.avatar || '../../../static/icon/my/图层 6.png'" mode="aspectFill"></image>
				<view class="avatar-edit">
					<image src="../../../static/icon/my/图层 6.png" mode="aspectFit"></image>
				</view>
			</view>
		</view> -->

		<!-- 个人信息表单 -->
		<view class="form-section">
			<view class="form-item" @click="changeAvatar">
				<text class="label">头像</text>
				<view class="value-container">
					<image class="avatar" :src="profileForm.avatar || '../../../static/icon/my/图层 6.png'" mode="aspectFill">
					</image>
					<image class="arrow" src="../../../static/icon/圆角矩形 1.png" mode="aspectFit"></image>
				</view>
			</view>
			<view class="form-item" @click="editField('name', '昵称', profileForm.name)">
				<text class="label">昵称</text>
				<view class="value-container">
					<text class="value">{{ profileForm.name }}</text>
					<image class="arrow" src="../../../static/icon/圆角矩形 1.png" mode="aspectFit"></image>
				</view>
			</view>

			<view class="form-item" @click="showDatePicker">
				<text class="label">生日</text>
				<view class="value-container">
					<text class="value">{{ profileForm.birthday }}</text>
					<image class="arrow" src="../../../static/icon/圆角矩形 1.png" mode="aspectFit"></image>
				</view>
			</view>

			<view class="form-item" @click="showGenderPicker">
				<text class="label">性别</text>
				<view class="value-container">
					<text class="value">{{ getGenderText(profileForm.gender) }}</text>
					<image class="arrow" src="../../../static/icon/圆角矩形 1.png" mode="aspectFit"></image>
				</view>
			</view>

			<view class="form-item">
				<text class="label">手机号</text>
				<view class="value-container">
					<text class="value">{{ formatPhone(profileForm.phone) || '19987458785' }}</text>
				</view>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<button class="save-btn" @click="saveProfile" :loading="saving">保存</button>
		</view>

		<!-- 编辑弹窗 -->
		<uni-popup ref="editPopup" type="center" v-if="showEditModal">
			<view class="edit-modal">
				<view class="modal-header">
					<text class="modal-title">编辑{{ currentEditField.label }}</text>
				</view>
				<view class="modal-content">
					<input class="edit-input" v-model="editValue" :placeholder="`请输入${currentEditField.label}`" :maxlength="20" />
				</view>
				<view class="modal-footer">
					<button class="btn-cancel" @click="closeEditModal">取消</button>
					<button class="btn-confirm" @click="confirmEdit">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 响应式数据
const saving = ref(false)
const showEditModal = ref(false)
const editValue = ref('')
const currentEditField = ref({})
const editPopup = ref(null)

// 个人资料表单
const profileForm = ref({
	avatar: '',
	name: '',
	birthday: '',
	gender: '',
	phone: ''
})

// 性别选项
const genderOptions = ref([
	{ label: '男', value: 'male' },
	{ label: '女', value: 'female' },
	{ label: '保密', value: 'secret' }
])

// 生命周期
onMounted(() => {
	loadProfile()
})

// 加载个人资料
const loadProfile = () => {
	// 从用户store中获取数据
	if (userStore.profile) {
		profileForm.value = {
			avatar: userStore.userAvatar || '',
			name: userStore.profile.counselorName || '',
			birthday: userStore.profile.birthdate || '',
			gender: userStore.profile.sex || '',
			phone: userStore.profile.userPhone || ''
		}
	}
}

// 格式化手机号
const formatPhone = (phone) => {
	if (!phone) return ''
	return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 获取性别文本
const getGenderText = (gender) => {
	const option = genderOptions.value.find(item => item.value === gender)
	return option ? option.label : '保密'
}

// 更换头像
const changeAvatar = () => {
	uni.chooseImage({
		count: 1,
		sizeType: ['compressed'],
		sourceType: ['album', 'camera'],
		success: (res) => {
			profileForm.value.avatar = res.tempFilePaths[0]
			uni.showToast({
				title: '头像更新成功',
				icon: 'success'
			})
		}
	})
}

// 编辑字段
const editField = (field, label, value) => {
	currentEditField.value = { field, label }
	editValue.value = value || ''
	showEditModal.value = true
	editPopup.value.open()
}

// 关闭编辑弹窗
const closeEditModal = () => {
	showEditModal.value = false
	editPopup.value.close()
}

// 确认编辑
const confirmEdit = () => {
	profileForm.value[currentEditField.value.field] = editValue.value
	closeEditModal()
}

// 显示日期选择器
const showDatePicker = () => {
	uni.showActionSheet({
		itemList: ['选择日期'],
		success: () => {
			// 使用系统日期选择器
			const date = new Date()
			const currentDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`

			// 创建一个临时的picker
			const pages = getCurrentPages()
			const currentPage = pages[pages.length - 1]

			// 模拟日期选择
			uni.showModal({
				title: '选择生日',
				content: '请输入生日（格式：YYYY-MM-DD）',
				editable: true,
				placeholderText: profileForm.value.birthday || '1990-09-10',
				success: (res) => {
					if (res.confirm && res.content) {
						profileForm.value.birthday = res.content
					}
				}
			})
		}
	})
}

// 显示性别选择器
const showGenderPicker = () => {
	const genderLabels = genderOptions.value.map(item => item.label)
	uni.showActionSheet({
		itemList: genderLabels,
		success: (res) => {
			const selectedGender = genderOptions.value[res.tapIndex]
			profileForm.value.gender = selectedGender.value
		}
	})
}

// 保存个人资料
const saveProfile = () => {
	saving.value = true

	// 模拟保存
	setTimeout(() => {
		saving.value = false
		uni.showToast({
			title: '保存成功',
			icon: 'success'
		})

		// 更新用户store
		userStore.updateProfile(profileForm.value)
	}, 1000)
}
</script>

<style scoped lang="scss">
.profile-container {
	background-color: #f8f8f8;
	min-height: calc(100vh - 24rpx);
	padding-bottom: 120rpx;
	padding-top: 24rpx;
}

.avatar-section {
	background-color: #fff;
	padding: 64rpx 32rpx;
	display: flex;
	justify-content: center;
	margin-bottom: 32rpx;
}

.avatar-item {
	position: relative;


	.avatar-edit {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 48rpx;
		height: 48rpx;
		background-color: #fff;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

		image {
			width: 24rpx;
			height: 24rpx;
		}
	}
}

.form-section {
	background-color: #fff;
}

.form-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f5f5f5;
	padding: 48rpx 32rpx;

	&:first-child {
		padding: 11rpx 32rpx;
	}

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f5f5f5;
	}
}

.label {
	font-size: 28rpx;
	color: #000;
}

.value-container {
	display: flex;
	align-items: center;

	.avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
	}
}

.value {
	font-size: 26rpx;
	color: #8A8788;
	margin-right: 16rpx;
}

.arrow {
	width: 24rpx;
	height: 24rpx;
}

.save-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 32rpx;
}

.save-btn {
	width: 100%;
	height: 98rpx;
	background: #A04571;
	border-radius: 18rpx;
	font-size: 32rpx;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;

	&::after {
		border: none;
	}
}

.edit-modal {
	width: 600rpx;
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
}

.modal-header {
	padding: 32rpx;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.modal-content {
	padding: 32rpx;
}

.edit-input {
	width: 100%;
	height: 80rpx;
	border: 1rpx solid #ddd;
	border-radius: 8rpx;
	padding: 0 16rpx;
	font-size: 28rpx;
}

.modal-footer {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;

	&::after {
		border: none;
	}
}

.btn-cancel {
	color: #666;
	border-right: 1rpx solid #f0f0f0;
}

.btn-confirm {
	color: #A04571;
}
</style>
