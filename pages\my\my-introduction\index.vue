<template>
	<view class="introduction-container">
		<view class="logo-section">
			<image class="logo" src="../../../static/icon/my/图层 6.png" mode="aspectFit"></image>
			<text class="app-name">熙桓心理</text>
			<text class="app-version">版本 1.0.0</text>
		</view>

		<view class="intro-section">
			<view class="intro-title">关于我们</view>
			<view class="intro-content">
				<text class="intro-text">
					熙桓心理是一个专业的心理健康服务平台，致力于为用户提供优质的心理咨询、心理测评、冥想课程等服务。我们拥有专业的心理咨询师团队，采用科学的心理学理论和方法，帮助用户解决心理困扰，提升心理健康水平。
				</text>
			</view>
		</view>

		<view class="features-section">
			<view class="features-title">核心功能</view>
			<view class="features-list">
				<view class="feature-item">
					<view class="feature-icon">
						<!-- <image src="../../../static/icon/my/个人.png" mode="aspectFit"></image> -->
					</view>
					<view class="feature-info">
						<text class="feature-name">专业咨询</text>
						<text class="feature-desc">资深心理咨询师一对一服务</text>
					</view>
				</view>

				<view class="feature-item">
					<view class="feature-icon">
						<!-- <image src="../../../static/icon/my/个人.png" mode="aspectFit"></image> -->
					</view>
					<view class="feature-info">
						<text class="feature-name">心理测评</text>
						<text class="feature-desc">科学的心理健康评估工具</text>
					</view>
				</view>

				<view class="feature-item">
					<view class="feature-icon">
						<!-- <image src="../../../static/icon/my/个人.png" mode="aspectFit"></image> -->
					</view>
					<view class="feature-info">
						<text class="feature-name">冥想课程</text>
						<text class="feature-desc">专业的冥想指导和练习</text>
					</view>
				</view>

				<view class="feature-item">
					<view class="feature-icon">
						<!-- <image src="../../../static/icon/my/个人.png" mode="aspectFit"></image> -->
					</view>
					<view class="feature-info">
						<text class="feature-name">课程学习</text>
						<text class="feature-desc">丰富的心理健康知识课程</text>
					</view>
				</view>
			</view>
		</view>

		<view class="contact-section">
			<view class="contact-title">联系我们</view>
			<view class="contact-list">
				<view class="contact-item" @click="callPhone">
					<text class="contact-label">客服电话：</text>
					<text class="contact-value">400-xxx-xxxx</text>
				</view>
				<view class="contact-item" @click="copyEmail">
					<text class="contact-label">邮箱地址：</text>
					<text class="contact-value"><EMAIL></text>
				</view>
				<view class="contact-item">
					<text class="contact-label">官方网站：</text>
					<text class="contact-value">www.xihuanxinli.com</text>
				</view>
			</view>
		</view>

		<view class="join-section" v-if="showJoinInfo">
			<view class="join-title">加入我们</view>
			<view class="join-content">
				<text class="join-text">
					如果您是专业的心理咨询师，欢迎加入我们的团队。我们提供良好的工作环境和发展机会，共同为用户的心理健康贡献力量。
				</text>
				<button class="join-btn" @click="contactHR">联系我们</button>
			</view>
		</view>

		<view class="copyright">
			<text class="copyright-text">© 2024 熙桓心理 版权所有</text>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const showJoinInfo = ref(false)

// 生命周期
onMounted(() => {
	// 根据页面来源判断是否显示招聘信息
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	const options = currentPage.options || {}

	// 如果是从"加入我们"入口进入，显示招聘信息
	if (options.type === 'join') {
		showJoinInfo.value = true
	}
})

// 拨打电话
const callPhone = () => {
	uni.makePhoneCall({
		phoneNumber: '400-xxx-xxxx'
	})
}

// 复制邮箱
const copyEmail = () => {
	uni.setClipboardData({
		data: '<EMAIL>',
		success: () => {
			uni.showToast({
				title: '邮箱地址已复制',
				icon: 'success'
			})
		}
	})
}

// 联系HR
const contactHR = () => {
	uni.showModal({
		title: '联系我们',
		content: '请发送简历至：<EMAIL>\n或拨打电话：400-xxx-xxxx',
		showCancel: false
	})
}
</script>

<style scoped lang="scss">
.introduction-container {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding: 32rpx;
}

.logo-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 64rpx 32rpx;
	text-align: center;
	margin-bottom: 32rpx;
}

.logo {
	width: 160rpx;
	height: 160rpx;
	margin-bottom: 24rpx;
}

.app-name {
	display: block;
	font-size: 40rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.app-version {
	font-size: 28rpx;
	color: #666;
}

.intro-section,
.features-section,
.contact-section,
.join-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
}

.intro-title,
.features-title,
.contact-title,
.join-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 24rpx;
}

.intro-text,
.join-text {
	font-size: 28rpx;
	line-height: 1.6;
	color: #666;
	text-indent: 2em;
}

.features-list {
	// 功能列表样式
}

.feature-item {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.feature-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 24rpx;

	image {
		width: 100%;
		height: 100%;
	}
}

.feature-info {
	flex: 1;
}

.feature-name {
	display: block;
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.feature-desc {
	font-size: 26rpx;
	color: #666;
}

.contact-list {
	// 联系方式列表样式
}

.contact-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}

	&:active {
		opacity: 0.7;
	}
}

.contact-label {
	font-size: 28rpx;
	color: #666;
	min-width: 160rpx;
}

.contact-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.join-content {
	text-align: center;
}

.join-btn {
	width: 200rpx;
	height: 72rpx;
	background: linear-gradient(to right, #A04571 0%, #6C2145 100%);
	border-radius: 36rpx;
	font-size: 28rpx;
	color: #fff;
	margin-top: 32rpx;

	&::after {
		border: none;
	}
}

.copyright {
	text-align: center;
	padding: 32rpx 0;
}

.copyright-text {
	font-size: 24rpx;
	color: #999;
}
</style>
