<template>
  <view class="universal-order-detail">
    <!-- 订单状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <view class="status-icon" :class="getStatusClass(orderData.status)">
          <uni-icons :type="getStatusIcon(orderData.status)" size="24" color="#fff"></uni-icons>
        </view>
        <view class="status-info">
          <text class="status-text">{{ getStatusText(orderData.status) }}</text>
          <text class="status-desc">{{ getStatusDesc(orderData.status) }}</text>
        </view>
      </view>
    </view>

    <!-- 商品信息卡片 -->
    <view class="product-card">
      <view class="product-header">
        <image class="shop-icon" :src="shopIcon" mode="aspectFit"></image>
        <text class="shop-name">{{ shopName }}</text>
        <view class="share-btn" @click="handleShare">
          <uni-icons type="redo" size="16" color="#999"></uni-icons>
        </view>
      </view>
      
      <view class="product-info">
        <image class="product-image" :src="orderData.productImage || getDefaultImage()" mode="aspectFill"></image>
        <view class="product-details">
          <text class="product-name">{{ orderData.productName }}</text>
          <view class="product-tags">
            <text v-for="tag in getProductTags()" :key="tag.text" class="tag" :class="tag.class">
              {{ tag.text }}
            </text>
          </view>
          <view class="product-price">
            <text class="price-label">实付：</text>
            <text class="price-value">¥ {{ orderData.paymentAmount || orderData.price }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 核销码/二维码 -->
    <view v-if="showQRCode" class="qr-card">
      <text class="qr-title">核销码</text>
      <view class="qr-code">
        <view class="qr-placeholder">
          <uni-icons type="scan" size="60" color="#ccc"></uni-icons>
        </view>
      </view>
      <text class="qr-desc">{{ qrCodeDesc }}</text>
    </view>

    <!-- 使用须知 -->
    <view v-if="showUsageRules" class="rules-card">
      <text class="rules-title">使用须知</text>
      <view class="rules-content">
        <view v-for="rule in usageRules" :key="rule.label" class="rule-item">
          <text class="rule-label">{{ rule.label }}</text>
          <text class="rule-value">{{ rule.value }}</text>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-card">
      <text class="info-title">订单信息</text>
      <view class="info-content">
        <view v-for="info in orderInfoList" :key="info.label" class="info-item">
          <text class="info-label">{{ info.label }}：</text>
          <text class="info-value">{{ info.value }}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button 
        v-for="action in availableActions" 
        :key="action.type"
        class="action-btn" 
        :class="action.class"
        @click="handleAction(action.type)"
      >
        {{ action.text }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { 
  getOrderTypeName, 
  getOrderStatusName, 
  ORDER_STATUS,
  ORDER_TYPES 
} from '@/api/payment.js'

// Props
const props = defineProps({
  // 订单数据
  orderData: {
    type: Object,
    required: true,
    default: () => ({})
  },
  // 商店图标
  shopIcon: {
    type: String,
    default: '../../static/icon/logo.png'
  },
  // 商店名称
  shopName: {
    type: String,
    default: '熙桓心理咨询阳店'
  },
  // 二维码描述
  qrCodeDesc: {
    type: String,
    default: '为了保证您的权益，请勿将此码泄露给他人'
  },
  // 自定义使用须知
  customRules: {
    type: Array,
    default: () => []
  },
  // 自定义操作按钮
  customActions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['action', 'share'])

// 计算属性
const showQRCode = computed(() => {
  return [ORDER_STATUS.PAID, ORDER_STATUS.PROCESSING, ORDER_STATUS.COMPLETED].includes(props.orderData.status)
})

const showUsageRules = computed(() => {
  return [ORDER_STATUS.PAID, ORDER_STATUS.PROCESSING, ORDER_STATUS.COMPLETED].includes(props.orderData.status)
})

// 获取商品标签
const getProductTags = () => {
  const tags = []
  const orderType = props.orderData.orderType

  if (orderType === ORDER_TYPES.CONSULTANT) {
    if (props.orderData.consultantLevel) {
      tags.push({ text: props.orderData.consultantLevel, class: 'consultant-tag' })
    }
    if (props.orderData.appointmentTime) {
      tags.push({ text: formatDateTime(props.orderData.appointmentTime), class: 'time-tag' })
    } else {
      tags.push({ text: '待预约', class: 'time-tag' })
    }
  } else {
    tags.push({ text: getOrderTypeName(orderType), class: 'type-tag' })
  }

  return tags
}

// 获取使用须知
const usageRules = computed(() => {
  if (props.customRules.length > 0) {
    return props.customRules
  }

  const defaultRules = [
    {
      label: '有效期',
      value: props.orderData.expireTime ? '购买后在有效期内均有效' : '购买后60天内有效'
    },
    {
      label: '预约说明',
      value: getBookingRules()
    },
    {
      label: '退款说明',
      value: '退款可在有效期内一天退款'
    }
  ]

  return defaultRules
})

// 获取订单信息列表
const orderInfoList = computed(() => {
  const infoList = []

  if (props.orderData.userPhone) {
    infoList.push({
      label: '手机号',
      value: formatPhone(props.orderData.userPhone)
    })
  }

  if (props.orderData.orderNo) {
    infoList.push({
      label: '订单号',
      value: props.orderData.orderNo
    })
  }

  if (props.orderData.createTime) {
    infoList.push({
      label: '下单时间',
      value: formatDateTime(props.orderData.createTime)
    })
  }

  if (props.orderData.payTime) {
    infoList.push({
      label: '支付时间',
      value: formatDateTime(props.orderData.payTime)
    })
  }

  return infoList
})

// 获取可用操作
const availableActions = computed(() => {
  if (props.customActions.length > 0) {
    return props.customActions
  }

  const actions = []
  const status = props.orderData.status

  // 联系客服 - 始终显示
  actions.push({
    type: 'contact-service',
    text: '联系客服',
    class: 'contact-btn'
  })

  // 根据状态显示不同操作
  if (status === ORDER_STATUS.PENDING) {
    actions.push({
      type: 'cancel-order',
      text: '取消订单',
      class: 'cancel-btn'
    })
    actions.push({
      type: 'pay-order',
      text: '立即支付',
      class: 'pay-btn'
    })
  } else if ([ORDER_STATUS.PAID, ORDER_STATUS.PROCESSING].includes(status)) {
    actions.push({
      type: 'refund',
      text: '退款',
      class: 'refund-btn'
    })
  }

  return actions
})

// 方法
const getStatusClass = (status) => {
  const classMap = {
    [ORDER_STATUS.PENDING]: 'status-pending',
    [ORDER_STATUS.PAID]: 'status-paid',
    [ORDER_STATUS.PROCESSING]: 'status-processing',
    [ORDER_STATUS.COMPLETED]: 'status-completed',
    [ORDER_STATUS.CANCELLED]: 'status-cancelled',
    [ORDER_STATUS.REFUNDING]: 'status-refunding',
    [ORDER_STATUS.REFUNDED]: 'status-refunded'
  }
  return classMap[status] || 'status-default'
}

const getStatusIcon = (status) => {
  const iconMap = {
    [ORDER_STATUS.PENDING]: 'time',
    [ORDER_STATUS.PAID]: 'checkmarkempty',
    [ORDER_STATUS.PROCESSING]: 'gear',
    [ORDER_STATUS.COMPLETED]: 'checkbox',
    [ORDER_STATUS.CANCELLED]: 'close',
    [ORDER_STATUS.REFUNDING]: 'undo',
    [ORDER_STATUS.REFUNDED]: 'undo'
  }
  return iconMap[status] || 'help'
}

const getStatusText = (status) => {
  return getOrderStatusName(status)
}

const getStatusDesc = (status) => {
  const descMap = {
    [ORDER_STATUS.PENDING]: '请尽快完成支付，超时订单将自动取消',
    [ORDER_STATUS.PAID]: '支付成功，可以开始使用服务',
    [ORDER_STATUS.PROCESSING]: '服务进行中，请耐心等待',
    [ORDER_STATUS.COMPLETED]: '服务已完成，感谢您的使用',
    [ORDER_STATUS.CANCELLED]: '订单已取消，如有疑问请联系客服',
    [ORDER_STATUS.REFUNDING]: '退款申请处理中，请耐心等待',
    [ORDER_STATUS.REFUNDED]: '退款已完成，资金将原路返回'
  }
  return descMap[status] || ''
}

const getDefaultImage = () => {
  const defaultImages = {
    [ORDER_TYPES.CONSULTANT]: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
    [ORDER_TYPES.COURSE]: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
    [ORDER_TYPES.MEDITATION]: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
    [ORDER_TYPES.ASSESSMENT]: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
  }
  return defaultImages[props.orderData.orderType] || defaultImages[ORDER_TYPES.CONSULTANT]
}

const getBookingRules = () => {
  const rulesMap = {
    [ORDER_TYPES.CONSULTANT]: '折扣券需在使用前进行预约',
    [ORDER_TYPES.COURSE]: '课程购买后可立即学习',
    [ORDER_TYPES.MEDITATION]: '冥想内容购买后可立即使用',
    [ORDER_TYPES.ASSESSMENT]: '测评购买后可立即开始'
  }
  return rulesMap[props.orderData.orderType] || '购买后可立即使用'
}

const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${year}/${month}/${day} ${hour}:${minute}`
}

const handleShare = () => {
  emit('share', props.orderData)
}

const handleAction = (actionType) => {
  emit('action', { type: actionType, orderData: props.orderData })
}
</script>

<style lang="scss" scoped>
.universal-order-detail {
  background-color: #f5f5f6;
  padding-bottom: 120rpx;
}

// 状态卡片
.status-card {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 40rpx 32rpx;

  .status-header {
    display: flex;
    align-items: center;

    .status-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;

      &.status-pending { background: #ff9500; }
      &.status-paid { background: #07c160; }
      &.status-processing { background: #576b95; }
      &.status-completed { background: #07c160; }
      &.status-cancelled { background: #fa5151; }
      &.status-refunding { background: #ff9500; }
      &.status-refunded { background: #999; }
    }

    .status-info {
      flex: 1;

      .status-text {
        display: block;
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
      }

      .status-desc {
        display: block;
        font-size: 24rpx;
        color: #999;
        line-height: 1.4;
      }
    }
  }
}

// 商品信息卡片
.product-card {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 32rpx;

  .product-header {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .shop-icon {
      width: 40rpx;
      height: 40rpx;
      border-radius: 8rpx;
      margin-right: 16rpx;
    }

    .shop-name {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .share-btn {
      padding: 8rpx;
    }
  }

  .product-info {
    display: flex;

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      margin-right: 24rpx;
    }

    .product-details {
      flex: 1;

      .product-name {
        display: block;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 16rpx;
        line-height: 1.4;
      }

      .product-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        margin-bottom: 16rpx;

        .tag {
          padding: 4rpx 12rpx;
          border-radius: 6rpx;
          font-size: 20rpx;
          color: #fff;

          &.consultant-tag { background: #a04571; }
          &.time-tag { background: #576b95; }
          &.type-tag { background: #07c160; }
        }
      }

      .product-price {
        display: flex;
        align-items: baseline;

        .price-label {
          font-size: 24rpx;
          color: #999;
          margin-right: 8rpx;
        }

        .price-value {
          font-size: 32rpx;
          color: #fa5151;
          font-weight: 600;
        }
      }
    }
  }
}

// 二维码卡片
.qr-card {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 40rpx 32rpx;
  text-align: center;

  .qr-title {
    display: block;
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 32rpx;
  }

  .qr-code {
    width: 300rpx;
    height: 300rpx;
    margin: 0 auto 24rpx;
    border: 2rpx dashed #ddd;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .qr-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }

  .qr-desc {
    display: block;
    font-size: 24rpx;
    color: #999;
    line-height: 1.4;
  }
}

// 使用须知卡片
.rules-card {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 32rpx;

  .rules-title {
    display: block;
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 24rpx;
  }

  .rules-content {
    .rule-item {
      display: flex;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .rule-label {
        width: 120rpx;
        font-size: 26rpx;
        color: #666;
        flex-shrink: 0;
      }

      .rule-value {
        flex: 1;
        font-size: 26rpx;
        color: #333;
        line-height: 1.4;
      }
    }
  }
}

// 订单信息卡片
.order-info-card {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 32rpx;

  .info-title {
    display: block;
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 24rpx;
  }

  .info-content {
    .info-item {
      display: flex;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        width: 120rpx;
        font-size: 26rpx;
        color: #666;
        flex-shrink: 0;
      }

      .info-value {
        flex: 1;
        font-size: 26rpx;
        color: #333;
        word-break: break-all;
      }
    }
  }
}

// 底部操作按钮
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 24rpx;
  z-index: 999;

  .action-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: 500;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &.contact-btn {
      background: #f5f5f6;
      color: #333;
    }

    &.cancel-btn {
      background: #f5f5f6;
      color: #333;
    }

    &.pay-btn {
      background: linear-gradient(90deg, #a04571, #923c65);
      color: #fff;
    }

    &.refund-btn {
      background: #ff9500;
      color: #fff;
    }

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
