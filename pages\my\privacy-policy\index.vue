<template>
	<view class="privacy-container">
		<view class="content">
			<view class="title">隐私协议</view>
			
			<view class="section">
				<view class="section-title">1. 信息收集</view>
				<view class="section-content">
					我们会收集您在使用服务时主动提供的信息，包括但不限于：注册信息、个人资料、咨询记录、使用偏好等。我们也会自动收集一些技术信息，如设备信息、日志信息等。
				</view>
			</view>

			<view class="section">
				<view class="section-title">2. 信息使用</view>
				<view class="section-content">
					我们使用收集的信息来：提供和改进服务、个性化推荐、客户支持、安全保障、法律合规等。我们不会将您的个人信息用于本协议未载明的其他用途。
				</view>
			</view>

			<view class="section">
				<view class="section-title">3. 信息共享</view>
				<view class="section-content">
					我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：获得您的明确同意、法律法规要求、保护我们的合法权益、紧急情况下保护用户安全。
				</view>
			</view>

			<view class="section">
				<view class="section-title">4. 信息存储</view>
				<view class="section-content">
					我们会在中华人民共和国境内存储您的个人信息。我们会采取合理的安全措施保护您的信息安全，包括数据加密、访问控制、安全审计等。
				</view>
			</view>

			<view class="section">
				<view class="section-title">5. 信息保护</view>
				<view class="section-content">
					我们建立了完善的数据安全管理制度，采用行业标准的安全技术和程序来保护您的个人信息。但请您理解，任何安全措施都无法做到绝对安全。
				</view>
			</view>

			<view class="section">
				<view class="section-title">6. 您的权利</view>
				<view class="section-content">
					您有权访问、更正、删除您的个人信息，有权撤回同意、投诉举报。如需行使这些权利，请通过平台客服联系我们。
				</view>
			</view>

			<view class="section">
				<view class="section-title">7. 未成年人保护</view>
				<view class="section-content">
					我们非常重视未成年人的个人信息保护。如果您是未成年人，请在监护人指导下使用我们的服务。
				</view>
			</view>

			<view class="section">
				<view class="section-title">8. 政策更新</view>
				<view class="section-content">
					我们可能会不时更新本隐私政策。更新后的政策将在平台上公布，并在您继续使用服务时生效。
				</view>
			</view>

			<view class="section">
				<view class="section-title">9. 联系我们</view>
				<view class="section-content">
					如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：
					<br>客服电话：400-xxx-xxxx
					<br>邮箱：<EMAIL>
				</view>
			</view>

			<view class="footer">
				<text class="update-time">最后更新时间：2024年1月1日</text>
			</view>
		</view>
	</view>
</template>

<script setup>
// 页面逻辑
</script>

<style scoped lang="scss">
.privacy-container {
	background-color: #fff;
	min-height: 100vh;
}

.content {
	padding: 32rpx;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 48rpx;
}

.section {
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
}

.section-content {
	font-size: 28rpx;
	line-height: 1.6;
	color: #666;
	text-indent: 2em;
}

.footer {
	margin-top: 64rpx;
	padding-top: 32rpx;
	border-top: 1rpx solid #f0f0f0;
	text-align: center;
}

.update-time {
	font-size: 24rpx;
	color: #999;
}
</style>
