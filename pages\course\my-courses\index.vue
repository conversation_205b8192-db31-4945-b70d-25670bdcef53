<template>
  <view class="my-courses-page">
    <!-- 顶部是 result-category-tabs 组件 -->
    <view class="result-category-tabs">
      <view v-for="(tab, index) in tabList" :key="tab.key" :class="['tab-item', { active: currentTab === tab.key }]"
        @click="switchTab(tab.key)">
        {{ tab.name }}
        <image v-if="currentTab === tab.key" class="tab-icon" src="../../../static/icon/形状 6-active.png"></image>
      </view>
    </view>

    <!-- 课程列表 -->
    <scroll-view class="course-list" scroll-y>
      <!-- 空状态 -->
      <view class="empty" v-if="currentList.length === 0">
        <text>暂无课程记录</text>
      </view>

      <!-- 使用 OrderListItem 组件 -->
      <view v-else class="list-container">
        <OrderListItem v-for="item in currentList" :key="item.id" :item="item" :type="'course'" @click="handleItemClick"
          @action="handleAction" />
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import OrderListItem from '@/components/OrderListItem/OrderListItem.vue'
import { getPurchasedCourses, getUserProgress } from '@/api/course'
import { useUserStore } from '@/stores/user'

// 响应式数据
const currentTab = ref('all')
const courseList = ref([])

// 分类标签配置
const tabList = [
  { key: 'all', name: '全部' },
  { key: 'learning', name: '学习中' },
  { key: 'completed', name: '已完成' }
]

// 课程订单数据
const courseOrders = ref([
  {
    id: 301,
    courseName: '心理学基础课程',
    courseCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
    status: 'pending',
    chapterCount: 12,
    duration: 180,
    progress: 0,
    createTime: '07/08 14:00',
    actualPrice: '99.9',
    type: 'course'
  },
  {
    id: 302,
    courseName: '认知行为疗法',
    courseCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
    status: 'in_progress',
    chapterCount: 8,
    duration: 120,
    progress: 45,
    createTime: '07/07 10:00',
    actualPrice: '199.9',
    type: 'course'
  },
  {
    id: 303,
    courseName: '情绪管理技巧',
    courseCover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
    status: 'completed',
    chapterCount: 6,
    duration: 90,
    progress: 100,
    createTime: '07/06 16:00',
    actualPrice: '79.9',
    type: 'course'
  }
])

const userStore = useUserStore()

// 计算当前分类下的列表
const currentList = computed(() => {
  // 将课程数据转换为 OrderListItem 组件期望的格式
  const transformedList = courseOrders.value.map(item => ({
    id: item.id,
    courseName: item.courseName,
    courseCover: item.courseCover,
    status: item.status,
    chapterCount: item.chapterCount,
    duration: item.duration,
    progress: item.progress,
    createTime: item.createTime,
    actualPrice: item.actualPrice,
    type: 'course',
    // 保留原始数据
    originalData: item
  }))

  // 根据选择的分类筛选
  if (currentTab.value === 'all') {
    return transformedList
  }
  return transformedList.filter(item => {
    if (currentTab.value === 'learning') {
      return item.status === 'in_progress'
    }
    if (currentTab.value === 'completed') {
      return item.status === 'completed'
    }
    return true
  })
})

// 方法
const switchTab = (key) => {
  currentTab.value = key
}

// 处理 OrderListItem 组件的点击事件
const handleItemClick = (item) => {
  console.log('点击课程项:', item)
  // 跳转到课程详情页
  uni.navigateTo({
    url: `/pages/course/detail/index?id=${item.id}`
  })
}

// 处理 OrderListItem 组件的操作事件
const handleAction = ({ action, item }) => {
  console.log('处理操作:', action, item)
  const originalData = item.originalData

  switch (action) {
    case 'detail':
      handleItemClick(item)
      break
    case 'use':
      // 开始学习
      uni.navigateTo({
        url: `/pages/course/learn/index?id=${item.id}`
      })
      break
    case 'continue':
      // 继续学习
      uni.navigateTo({
        url: `/pages/course/learn/index?id=${item.id}&chapter=${originalData?.currentChapter || 1}`
      })
      break
    default:
      handleItemClick(item)
      break
  }
}

// 加载课程数据
const loadCourseData = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    // 这里可以调用实际的API获取课程数据
    // const res = await getPurchasedCourses()
    // courseOrders.value = res.data || []
    console.log('加载课程数据')
  } catch (error) {
    console.error('获取课程数据失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 页面加载时初始化数据
onLoad(() => {
  loadCourseData()
})
</script>

<style lang="scss" scoped>
.my-courses-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

// 使用与测评页面相同的样式
.result-category-tabs {
  display: flex;
  background-color: #fff;
  padding: 0 31rpx;
  margin-bottom: 16rpx;

  .tab-item {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 28rpx;
    color: #999;

    &.active {
      color: #333;
      font-weight: 500;

      .tab-icon {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 6rpx;
      }
    }
  }
}

.course-list {
  flex: 1;
  padding: 0 31rpx;
}

// .empty {
//   text-align: center;
//   padding: 100rpx 0;
//   color: #999;
//   font-size: 28rpx;
// }

.list-container {
  padding-bottom: 20rpx;
}
</style>
